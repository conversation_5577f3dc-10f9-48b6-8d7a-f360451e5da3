<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - Full Stack Web Developer</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        /* CSS Variables for colors */
        :root {
            --primary-color: #2563eb; /* blue-600 */
            --background-color: #f8fafc; /* slate-50 */
            --text-color: #1e293b; /* slate-900 */
            --secondary-text-color: #64748b; /* slate-500 */
            --border-color: #e2e8f0; /* slate-200 */
            --card-bg: #ffffff;
            --card-hover-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
        }

        /* Basic Reset & Box Sizing */
        *, *::before, *::after {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 900px;
            margin: 30px auto;
            padding: 30px;
            background-color: var(--card-bg);
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        /* Header Styles */
        header {
            text-align: center;
            padding-bottom: 25px;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 30px;
        }

        header h1 {
            color: var(--primary-color);
            font-size: 3em;
            margin-bottom: 8px;
            font-weight: 700;
        }

        header .title {
            font-size: 1.5em;
            color: var(--secondary-text-color);
            margin-top: 0;
            font-weight: 400;
        }

        .contact-info {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px 30px; /* row-gap column-gap */
            margin-top: 25px;
            font-size: 0.95em;
        }

        .contact-info p {
            margin: 0;
            display: flex;
            align-items: center;
        }

        .contact-info a {
            color: var(--text-color);
            text-decoration: none;
            transition: color 0.2s ease-in-out;
            margin-left: 5px; /* Space for a potential icon */
        }

        .contact-info a:hover {
            color: var(--primary-color);
            text-decoration: underline;
        }

        /* Section Styles */
        section {
            margin-bottom: 35px;
            padding-bottom: 25px;
            border-bottom: 1px solid var(--border-color);
        }

        section:last-of-type { /* Last section should not have a border-bottom */
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        h2 {
            color: var(--primary-color);
            font-size: 2.2em;
            margin-bottom: 20px;
            font-weight: 600;
            border-bottom: 3px solid var(--primary-color);
            display: inline-block; /* Makes border-bottom only as wide as text */
            padding-bottom: 8px;
            letter-spacing: 0.5px;
        }

        h3 {
            color: var(--text-color);
            font-size: 1.5em;
            margin-top: 25px;
            margin-bottom: 8px;
            font-weight: 600;
        }

        h4 {
            color: var(--text-color);
            font-size: 1.2em;
            margin-top: 15px;
            margin-bottom: 5px;
            font-weight: 600;
        }

        p {
            margin-bottom: 10px;
        }

        ul {
            list-style-type: disc;
            margin: 0 0 10px 25px;
            padding: 0;
        }

        ul ul { /* Nested lists */
            list-style-type: circle;
            margin-left: 20px;
            margin-top: 5px;
        }

        li {
            margin-bottom: 8px;
        }

        /* Specific Section Styling */
        .experience-item, .project-item, .education-item {
            margin-bottom: 25px;
            padding: 20px;
            background-color: #fcfdfe; /* Slight off-white background */
            border-left: 5px solid var(--primary-color);
            border-radius: 6px;
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }

        .experience-item:hover, .project-item:hover, .education-item:hover {
            transform: translateY(-3px);
            box-shadow: var(--card-hover-shadow);
        }

        .experience-item:last-of-type, .project-item:last-of-type, .education-item:last-of-type {
            margin-bottom: 0;
        }

        .experience-item .date, .education-item .date {
            color: var(--secondary-text-color);
            font-size: 0.95em;
            margin-top: -5px;
            margin-bottom: 15px;
            font-weight: 400;
        }

        .skills-list {
            list-style-type: none;
            margin-left: 0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 15px;
        }

        .skills-list li {
            background-color: #f0f4f8; /* Light grey for skill tags */
            padding: 12px 18px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            font-size: 0.95em;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .skills-list li strong {
            color: var(--primary-color);
            margin-right: 10px;
            font-weight: 600;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                margin: 15px;
                padding: 20px;
            }

            header h1 {
                font-size: 2.5em;
            }

            header .title {
                font-size: 1.3em;
            }

            .contact-info {
                flex-direction: column;
                align-items: flex-start; /* Align left for better readability on small screens */
                gap: 8px;
                padding-left: 10px; /* Indent contact info slightly */
            }

            h2 {
                font-size: 1.8em;
                margin-bottom: 15px;
                padding-bottom: 5px;
                display: block; /* Make border span full width */
                text-align: center; /* Center section titles on small screens */
                border-bottom: 2px solid var(--primary-color);
            }

            h3 {
                font-size: 1.3em;
            }

            h4 {
                font-size: 1.1em;
            }

            ul {
                margin-left: 20px;
            }

            .skills-list {
                grid-template-columns: 1fr; /* Stack skills on small screens */
            }

            .experience-item, .project-item, .education-item {
                padding: 15px;
            }
        }

        @media (max-width: 480px) {
            header h1 {
                font-size: 2em;
            }

            header .title {
                font-size: 1.1em;
            }

            .contact-info {
                font-size: 0.9em;
            }

            h2 {
                font-size: 1.6em;
            }
            .skills-list li {
                padding: 10px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Manzoor Ah Chopan</h1>
            <p class="title">Full Stack Web Developer</p>
            <div class="contact-info">
                <p>📞 <a href="tel:7780883346">7780883346</a></p>
                <p>✉ <a href="mailto:<EMAIL>"><EMAIL></a></p>
                <p>📍 Srinagar, Jammu & Kashmir</p>
                <p>🔗 <a href="https://linkedin.com/in/manzoor-chopan-074b14201" target="_blank" rel="noopener noreferrer">LinkedIn</a></p>
                <p>🔗 <a href="https://github.com/mchopan" target="_blank" rel="noopener noreferrer">GitHub</a></p>
                <p>🔗 <a href="https://mchopan.github.io/portfolio" target="_blank" rel="noopener noreferrer">Portfolio</a></p>
            </div>
        </header>

        <main>
            <section id="summary">
                <h2>Professional Summary</h2>
                <p>Full Stack Web Developer with experience in designing and developing web applications using modern technologies. Proficient in front-end and back-end development, with a focus on creating user-friendly interfaces and scalable solutions. Adept at collaborating with cross-functional teams to deliver impactful digital experiences.</p>
            </section>

            <section id="experience">
                <h2>Professional Experience</h2>
                <div class="experience-item">
                    <h3>Cogveel Technologies | Full Stack Developer</h3>
                    <p class="date">Sep 2023 – Present</p>
                    <ul>
                        <li>Developed and maintained web applications using React, Node.js, and MongoDB.</li>
                        <li>Built cross-platform mobile applications using React Native.</li>
                        <li>Collaborated with teams to design and implement new features.</li>
                        <li>Enhanced application performance and user experience.</li>
                    </ul>
                    <h4>Key Projects:</h4>
                    <ul>
                        <li><strong>Neuquip:</strong> Solely developed the frontend of this AI-powered application with features including:
                            <ul>
                                <li>Uploading files (PDF, Excel, TXT, Word) and enabling conversational interaction with document content.</li>
                                <li>Generating analytics and exporting them into a sketchbook for document creation.</li>
                                <li>Creating workflows for sketchbooks, with sharing functionality among multiple users.</li>
                                <li>Designing an enhanced user interface to support complex workflows.</li>
                            </ul>
                        </li>
                        <li><strong>Apple Doc:</strong> Redesigned the UI and added new features to this orchid plant treatment app.</li>
                        <li><strong>Wabi Sabi:</strong> Contributed to the development of an e-commerce platform as part of a team.</li>
                        <li><strong>Quick Load:</strong> Created a booking app for trucks from scratch, handling both backend and frontend development.</li>
                    </ul>
                </div>

                <div class="experience-item">
                    <h3>Red Stag Labs | Full Stack Developer</h3>
                    <p class="date">Nov 2022 – May 2023</p>
                    <ul>
                        <li>Designed and developed a dynamic full-stack web application using ReactJS, Material-UI, CSS, Spring Boot, and MySQL.</li>
                        <li>Focused on creating user-friendly interfaces and enhancing interactivity.</li>
                        <li>Contributed to the project marvelminds.in.</li>
                    </ul>
                </div>

                <div class="experience-item">
                    <h3>Oxford Skill and Education Institute | Web Developer</h3>
                    <p class="date">Feb 2022 – Mar 2022</p>
                    <ul>
                        <li>Developed a comprehensive registration platform for students.</li>
                        <li>Improved the institute's online presence and streamlined the registration process.</li>
                    </ul>
                </div>
            </section>

            <section id="personal-projects">
                <h2>Personal Projects</h2>
                <div class="project-item">
                    <h3>site-bot (NPM Package)</h3>
                    <p>Developed a highly customizable chatbot as an npm package designed to work seamlessly with any JavaScript framework or library.</p>
                    <ul>
                        <li><strong>AI Integration:</strong> Utilizes an AI API key provided by the library user, enabling dynamic and context-aware interactions.</li>
                        <li><strong>Customizable and Flexible:</strong> Designed to be easily integrated into any website, providing developers with the flexibility to customize the chatbot's behavior and appearance.</li>
                        <li><strong>Cross-Platform Compatibility:</strong> Built with compatibility across modern JavaScript frameworks, making it adaptable for diverse project requirements.</li>
                        <li><strong>Developer-Friendly:</strong> Created with a focus on simplicity and ease of use, allowing developers to set up the chatbot with minimal configuration.</li>
                        <li><strong>Published on NPM:</strong> The package is published on npm, making it easily accessible for public use. (search in npm : mchopan/sitebot)</li>
                    </ul>
                </div>
                <div class="project-item">
                    <h3>Campus Command (React Native | Final Year Project)</h3>
                    <ul>
                        <li>Designed and developed a messaging app tailored for educational institutions.</li>
                        <li>Enabled colleges to create semesters/groups and manage student registrations by registration numbers.</li>
                        <li>Built a student dashboard that displays only relevant semester/group data.</li>
                        <li>Integrated features like group chat, updates, and collaboration tools.</li>
                        <li>Solely responsible for the app's design, development, and deployment.</li>
                    </ul>
                </div>
                <div class="project-item">
                    <h3>Other Projects:</h3>
                    <ul>
                        <li>Portfolio Website: Showcased professional skills and projects.</li>
                        <li>CASET College Website: Built a dynamic website for the college.</li>
                        <li>Mehndi Artist Portfolio: Designed an elegant portfolio for a mehndi artist.</li>
                        <li>News Website: Developed a news website leveraging the News API.</li>
                        <li>Facebook Clone: Built a front-end clone of Facebook.</li>
                        <li>Itinerary Website: Created a website for booking travel-related services.</li>
                    </ul>
                </div>
            </section>

            <section id="education">
                <h2>Education</h2>
                <div class="education-item">
                    <h3>Caset College of Computer Science</h3>
                    <p class="date">Bachelor of Computer Applications (BCA) | 2021 – 2023</p>
                </div>
                <div class="education-item">
                    <h3>Govt Boys Higher Secondary (JKBOSE)</h3>
                    <p class="date">2019 – 2020</p>
                </div>
                <div class="education-item">
                    <h3>Maulana Azad Education Foundation</h3>
                    <p class="date">Web Designing & Publishing Assistant | 2017 – 2018</p>
                </div>
                <div class="education-item">
                    <h3>Govt Boys High School (JKBOSE)</h3>
                    <p class="date">2016 – 2017</p>
                </div>
            </section>

            <section id="skills">
                <h2>Skills</h2>
                <ul class="skills-list">
                    <li><strong>Front-End:</strong> React, HTML, CSS, JavaScript, Material-UI</li>
                    <li><strong>Back-End:</strong> Node.js, Spring Boot, MySQL, MongoDB</li>
                    <li><strong>Mobile Development:</strong> React Native</li>
                    <li><strong>Other:</strong> Git, Web Designing & Publishing</li>
                </ul>
            </section>

            <section id="certifications">
                <h2>Certifications</h2>
                <ul>
                    <li>Full Stack Web Development – Red Stag Labs, Qamarwari, Srinagar</li>
                    <li>Linux Fundamentals – Kimo.ai</li>
                    <li>Hands-On Linux System Administration – Kimo.ai</li>
                    <li>Data Structures and Algorithms – Kimo.ai</li>
                </ul>
            </section>
        </main>
    </div>
</body>
</html>