<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - Full Stack Web Developer</title>
    <style>
        /* General Styles */
        :root {
            --primary-color: #2563eb;
            --background-color: #f8fafc;
            --text-color: #1e293b;
            --light-gray: #e2e8f0;
            --dark-gray: #64748b;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
        }

        .container {
            max-width: 900px;
            width: 100%;
            background-color: #ffffff;
            padding: 30px 40px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        }

        /* Header Section */
        header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid var(--light-gray);
        }

        header h1 {
            color: var(--primary-color);
            margin: 0;
            font-size: 2.8em;
            font-weight: 700;
            letter-spacing: -0.5px;
        }

        header p.title {
            font-size: 1.4em;
            color: var(--dark-gray);
            margin-top: 5px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .contact-info, .social-links {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px 30px;
            margin-top: 15px;
            font-size: 0.95em;
            color: var(--dark-gray);
        }

        .contact-info span, .social-links a {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .social-links a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .social-links a:hover {
            color: #1a4ed8; /* Darker shade of primary */
            text-decoration: underline;
        }

        /* Section Headings */
        h2 {
            color: var(--primary-color);
            font-size: 1.8em;
            margin-top: 35px;
            margin-bottom: 20px;
            padding-bottom: 8px;
            border-bottom: 2px solid var(--primary-color);
            display: inline-block;
            letter-spacing: 0.5px;
        }

        /* Professional Summary */
        .summary p {
            font-size: 1.1em;
            line-height: 1.7;
            text-align: justify;
        }

        /* Experience & Projects */
        .experience-item, .project-item {
            margin-bottom: 30px;
            padding-left: 15px;
            border-left: 3px solid var(--light-gray);
        }

        .experience-item h3, .project-item h3 {
            color: var(--text-color);
            font-size: 1.3em;
            margin-top: 0;
            margin-bottom: 5px;
            display: flex;
            justify-content: space-between;
            align-items: baseline;
        }
        .experience-item h3 span.company-name {
            font-weight: 600;
            color: var(--primary-color);
        }
        .experience-item h3 span.role {
            font-weight: 500;
            color: var(--text-color);
        }
        .experience-item h3 span.dates {
            font-size: 0.9em;
            color: var(--dark-gray);
            white-space: nowrap;
        }

        .experience-item ul, .project-item ul {
            list-style: none;
            padding-left: 0;
            margin-top: 10px;
        }

        .experience-item ul li, .project-item ul li {
            position: relative;
            padding-left: 25px;
            margin-bottom: 8px;
            font-size: 1em;
            line-height: 1.5;
        }

        .experience-item ul li::before, .project-item ul li::before {
            content: '•';
            color: var(--primary-color);
            position: absolute;
            left: 0;
            font-size: 1.2em;
            line-height: 1;
        }

        .project-item p {
            margin-top: 5px;
            margin-bottom: 10px;
            font-size: 0.95em;
            color: var(--dark-gray);
        }

        .project-item ul.sub-list {
            list-style: none;
            padding-left: 20px;
            margin-top: 5px;
            margin-bottom: 10px;
        }

        .project-item ul.sub-list li {
            position: relative;
            padding-left: 20px;
            margin-bottom: 5px;
            font-size: 0.9em;
        }

        .project-item ul.sub-list li::before {
            content: '–';
            color: var(--dark-gray);
            position: absolute;
            left: 0;
            font-size: 1.1em;
            line-height: 1;
        }

        /* Education, Skills, Certifications */
        .education-item, .skills-list, .certifications-list {
            margin-bottom: 25px;
        }

        .education-item {
            margin-bottom: 15px;
            padding-left: 15px;
            border-left: 3px solid var(--light-gray);
        }

        .education-item p {
            margin: 0;
            font-size: 1.1em;
            font-weight: 500;
        }

        .education-item span.institute {
            color: var(--primary-color);
            font-weight: 600;
        }
        .education-item span.degree {
            color: var(--text-color);
        }
        .education-item span.edu-dates {
            font-size: 0.9em;
            color: var(--dark-gray);
            display: block;
            margin-top: 3px;
        }


        .skills-list ul {
            display: flex;
            flex-wrap: wrap;
            list-style: none;
            padding: 0;
            margin: 0;
            gap: 10px;
        }

        .skills-list ul li {
            background-color: var(--light-gray);
            color: var(--text-color);
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
            white-space: nowrap;
        }
        
        .certifications-list ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .certifications-list ul li {
            position: relative;
            padding-left: 25px;
            margin-bottom: 8px;
            font-size: 1em;
        }
        .certifications-list ul li::before {
            content: '•';
            color: var(--primary-color);
            position: absolute;
            left: 0;
            font-size: 1.2em;
            line-height: 1;
        }

        /* Icons (basic text-based for simplicity) */
        .icon-phone::before { content: '📞 '; }
        .icon-email::before { content: '✉ '; }
        .icon-address::before { content: '📍 '; }
        .icon-linkedin::before { content: '🔗 '; }
        .icon-github::before { content: '🔗 '; }
        .icon-portfolio::before { content: '🔗 '; }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 25px 25px;
            }

            header h1 {
                font-size: 2.2em;
            }

            header p.title {
                font-size: 1.1em;
            }

            .contact-info, .social-links {
                flex-direction: column;
                gap: 10px;
                align-items: flex-start;
                font-size: 0.9em;
            }
            .contact-info span, .social-links a {
                width: 100%;
                justify-content: flex-start;
            }

            h2 {
                font-size: 1.6em;
                margin-top: 30px;
                margin-bottom: 15px;
            }

            .experience-item h3 {
                flex-direction: column;
                align-items: flex-start;
                font-size: 1.2em;
            }
            .experience-item h3 span.dates {
                margin-top: 5px;
            }

            .summary p {
                font-size: 1em;
            }

            .skills-list ul {
                gap: 8px;
            }

            .skills-list ul li {
                padding: 6px 12px;
                font-size: 0.85em;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 15px;
            }
            .container {
                padding: 20px 15px;
            }
            header h1 {
                font-size: 1.8em;
            }
            header p.title {
                font-size: 1em;
            }
            h2 {
                font-size: 1.4em;
            }
            .experience-item ul li, .project-item ul li, .education-item p, .certifications-list ul li {
                font-size: 0.95em;
            }
            .project-item ul.sub-list li {
                font-size: 0.85em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Manzoor Ah Chopan</h1>
            <p class="title">Full Stack Web Developer</p>
            <div class="contact-info">
                <span class="icon-phone">7780883346</span>
                <span class="icon-email"><a href="mailto:<EMAIL>"><EMAIL></a></span>
                <span class="icon-address">Srinagar, Jammu & Kashmir</span>
            </div>
            <div class="social-links">
                <a href="https://linkedin.com/in/manzoor-chopan-074b14201" target="_blank" rel="noopener noreferrer" class="icon-linkedin">LinkedIn</a>
                <a href="https://github.com/mchopan" target="_blank" rel="noopener noreferrer" class="icon-github">GitHub</a>
                <a href="https://mchopan.github.io/portfolio" target="_blank" rel="noopener noreferrer" class="icon-portfolio">Portfolio</a>
            </div>
        </header>

        <main>
            <section class="summary">
                <h2>Professional Summary</h2>
                <p>Full Stack Web Developer with experience in designing and developing web applications using modern technologies. Proficient in front-end and back-end development, with a focus on creating user-friendly interfaces and scalable solutions. Adept at collaborating with cross-functional teams to deliver impactful digital experiences.</p>
            </section>

            <section class="experience">
                <h2>Professional Experience</h2>

                <div class="experience-item">
                    <h3>
                        <span class="company-name">Cogveel Technologies</span> | <span class="role">Full Stack Developer</span>
                        <span class="dates">Sep 2023 – Present</span>
                    </h3>
                    <ul>
                        <li>Developed and maintained web applications using React, Node.js, and MongoDB.</li>
                        <li>Built cross-platform mobile applications using React Native.</li>
                        <li>Collaborated with teams to design and implement new features.</li>
                        <li>Enhanced application performance and user experience.</li>
                    </ul>
                    <div class="projects-within-experience">
                        <h4>Key Projects:</h4>
                        <div class="project-item">
                            <p><strong>Neuquip:</strong> Solely developed the frontend of this AI-powered application with features including:</p>
                            <ul class="sub-list">
                                <li>Uploading files (PDF, Excel, TXT, Word) and enabling conversational interaction with document content.</li>
                                <li>Generating analytics and exporting them into a sketchbook for document creation.</li>
                                <li>Creating workflows for sketchbooks, with sharing functionality among multiple users.</li>
                                <li>Designing an enhanced user interface to support complex workflows.</li>
                            </ul>
                        </div>
                        <div class="project-item">
                            <p><strong>Apple Doc:</strong> Redesigned the UI and added new features to this orchid plant treatment app.</p>
                        </div>
                        <div class="project-item">
                            <p><strong>Wabi Sabi:</strong> Contributed to the development of an e-commerce platform as part of a team.</p>
                        </div>
                        <div class="project-item">
                            <p><strong>Quick Load:</strong> Created a booking app for trucks from scratch, handling both backend and frontend development.</p>
                        </div>
                    </div>
                </div>

                <div class="experience-item">
                    <h3>
                        <span class="company-name">Red Stag Labs</span> | <span class="role">Full Stack Developer</span>
                        <span class="dates">Nov 2022 – May 2023</span>
                    </h3>
                    <ul>
                        <li>Designed and developed a dynamic full-stack web application using ReactJS, Material-UI, CSS, Spring Boot, and MySQL.</li>
                        <li>Focused on creating user-friendly interfaces and enhancing interactivity.</li>
                        <li>Contributed to the project marvelminds.in.</li>
                    </ul>
                </div>

                <div class="experience-item">
                    <h3>
                        <span class="company-name">Oxford Skill and Education Institute</span> | <span class="role">Web Developer</span>
                        <span class="dates">Feb 2022 – Mar 2022</span>
                    </h3>
                    <ul>
                        <li>Developed a comprehensive registration platform for students.</li>
                        <li>Improved the institute's online presence and streamlined the registration process.</li>
                    </ul>
                </div>
            </section>

            <section class="personal-projects">
                <h2>Personal Projects</h2>
                <div class="project-item">
                    <h3>site-bot (NPM Package)</h3>
                    <ul>
                        <li>Developed a highly customizable chatbot as an npm package designed to work seamlessly with any JavaScript framework or library.</li>
                        <ul class="sub-list">
                            <li><strong>AI Integration:</strong> Utilizes an AI API key provided by the library user, enabling dynamic and context-aware interactions.</li>
                            <li><strong>Customizable and Flexible:</strong> Designed to be easily integrated into any website, providing developers with the flexibility to customize the chatbot's behavior and appearance.</li>
                            <li><strong>Cross-Platform Compatibility:</strong> Built with compatibility across modern JavaScript frameworks, making it adaptable for diverse project requirements.</li>
                            <li><strong>Developer-Friendly:</strong> Created with a focus on simplicity and ease of use, allowing developers to set up the chatbot with minimal configuration.</li>
                            <li><strong>Published on NPM:</strong> The package is published on npm, making it easily accessible for public use. (search in npm : mchopan/sitebot)</li>
                        </ul>
                    </ul>
                </div>
                <div class="project-item">
                    <h3>Campus Command (React Native | Final Year Project)</h3>
                    <ul>
                        <li>Designed and developed a messaging app tailored for educational institutions.</li>
                        <li>Enabled colleges to create semesters/groups and manage student registrations by registration numbers.</li>
                        <li>Built a student dashboard that displays only relevant semester/group data.</li>
                        <li>Integrated features like group chat, updates, and collaboration tools.</li>
                        <li>Solely responsible for the app's design, development, and deployment.</li>
                    </ul>
                </div>
                <div class="project-item">
                    <h3>Portfolio Website</h3>
                    <ul>
                        <li>Showcased professional skills and projects.</li>
                    </ul>
                </div>
                <div class="project-item">
                    <h3>CASET College Website</h3>
                    <ul>
                        <li>Built a dynamic website for the college.</li>
                    </ul>
                </div>
                <div class="project-item">
                    <h3>Mehndi Artist Portfolio</h3>
                    <ul>
                        <li>Designed an elegant portfolio for a mehndi artist.</li>
                    </ul>
                </div>
                <div class="project-item">
                    <h3>News Website</h3>
                    <ul>
                        <li>Developed a news website leveraging the News API.</li>
                    </ul>
                </div>
                <div class="project-item">
                    <h3>Facebook Clone</h3>
                    <ul>
                        <li>Built a front-end clone of Facebook.</li>
                    </ul>
                </div>
                <div class="project-item">
                    <h3>Itinerary Website</h3>
                    <ul>
                        <li>Created a website for booking travel-related services.</li>
                    </ul>
                </div>
            </section>

            <section class="education">
                <h2>Education</h2>
                <div class="education-item">
                    <p><span class="institute">Caset College of Computer Science</span></p>
                    <p><span class="degree">Bachelor of Computer Applications (BCA)</span> | <span class="edu-dates">2021 – 2023</span></p>
                </div>
                <div class="education-item">
                    <p><span class="institute">Govt Boys Higher Secondary (JKBOSE)</span> | <span class="edu-dates">2019 – 2020</span></p>
                </div>
                <div class="education-item">
                    <p><span class="institute">Maulana Azad Education Foundation</span></p>
                    <p><span class="degree">Web Designing & Publishing Assistant</span> | <span class="edu-dates">2017 – 2018</span></p>
                </div>
                <div class="education-item">
                    <p><span class="institute">Govt Boys High School (JKBOSE)</span> | <span class="edu-dates">2016 – 2017</span></p>
                </div>
            </section>

            <section class="skills">
                <h2>Skills</h2>
                <div class="skills-list">
                    <ul>
                        <li>Front-End: React</li>
                        <li>Front-End: HTML</li>
                        <li>Front-End: CSS</li>
                        <li>Front-End: JavaScript</li>
                        <li>Front-End: Material-UI</li>
                        <li>Back-End: Node.js</li>
                        <li>Back-End: Spring Boot</li>
                        <li>Back-End: MySQL</li>
                        <li>Back-End: MongoDB</li>
                        <li>Mobile Development: React Native</li>
                        <li>Other: Git</li>
                        <li>Other: Web Designing & Publishing</li>
                    </ul>
                </div>
            </section>

            <section class="certifications">
                <h2>Certifications</h2>
                <div class="certifications-list">
                    <ul>
                        <li>Full Stack Web Development – Red Stag Labs, Qamarwari, Srinagar</li>
                        <li>Linux Fundamentals – Kimo.ai</li>
                        <li>Hands-On Linux System Administration – Kimo.ai</li>
                        <li>Data Structures and Algorithms – Kimo.ai</li>
                    </ul>
                </div>
            </section>
        </main>
    </div>
</body>
</html>