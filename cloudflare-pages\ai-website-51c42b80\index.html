<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - Full Stack Web Developer</title>
    <style>
        /* Base Styles */
        :root {
            --primary-color: #2563eb;
            --background-color: #f8fafc;
            --text-color: #1e293b;
            --light-gray: #e2e8f0;
            --dark-gray: #475569;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--background-color);
            padding: 20px;
            display: flex;
            justify-content: center;
            min-height: 100vh;
        }

        .container {
            max-width: 900px;
            width: 100%;
            background-color: #ffffff;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        }

        /* Typography */
        h1 {
            color: var(--primary-color);
            font-size: 2.8em;
            margin-bottom: 5px;
            text-align: center;
        }

        h2 {
            font-size: 1.6em;
            color: var(--dark-gray);
            margin-bottom: 20px;
            text-align: center;
            font-weight: 500;
        }

        h3 {
            font-size: 1.8em;
            color: var(--primary-color);
            margin-top: 30px;
            margin-bottom: 15px;
            border-bottom: 2px solid var(--light-gray);
            padding-bottom: 5px;
        }

        h4 {
            font-size: 1.2em;
            color: var(--text-color);
            margin-bottom: 5px;
        }

        p {
            margin-bottom: 10px;
        }

        ul {
            list-style-type: none; /* Remove default bullet */
            padding-left: 0;
            margin-bottom: 15px;
        }

        ul li {
            position: relative;
            padding-left: 25px; /* Space for custom bullet */
            margin-bottom: 8px;
        }

        ul li::before {
            content: '•'; /* Custom bullet point */
            color: var(--primary-color);
            font-size: 1.2em;
            position: absolute;
            left: 0;
            top: 0;
        }

        ul ul { /* Nested lists for projects */
            margin-top: 5px;
            margin-bottom: 0;
            padding-left: 20px;
        }

        ul ul li::before {
            content: '–'; /* Different bullet for nested lists */
            color: var(--dark-gray);
            font-size: 1em;
        }

        a {
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        a:hover {
            text-decoration: underline;
            color: #1d4ed8; /* Slightly darker primary on hover */
        }

        /* Header / Contact Info */
        .contact-info {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px 30px;
            margin-bottom: 30px;
            font-size: 0.95em;
            color: var(--dark-gray);
            border-bottom: 1px solid var(--light-gray);
            padding-bottom: 20px;
        }

        .contact-info div {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .contact-info .icon {
            color: var(--primary-color);
            font-size: 1.1em;
        }

        /* Sections */
        section {
            margin-bottom: 30px;
            padding-top: 10px;
        }

        /* Experience & Projects Specifics */
        .experience-item, .project-item {
            margin-bottom: 25px;
            padding-left: 10px;
            border-left: 3px solid var(--light-gray);
        }

        .experience-item:last-child, .project-item:last-child {
            margin-bottom: 0;
        }

        .experience-item h4 {
            margin-bottom: 0;
        }

        .experience-item .role-dates {
            font-style: italic;
            color: var(--dark-gray);
            margin-bottom: 10px;
            display: block;
            font-size: 0.95em;
        }

        .skills-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px 20px;
            margin-top: 10px;
        }

        .skill-category {
            flex-basis: 100%;
            margin-bottom: 5px;
        }

        .skill-category strong {
            display: block;
            margin-bottom: 5px;
            color: var(--primary-color);
            font-size: 1.1em;
        }

        .skill-category span {
            display: block;
            color: var(--text-color);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }

            h1 {
                font-size: 2.2em;
            }

            h2 {
                font-size: 1.3em;
            }

            h3 {
                font-size: 1.5em;
                margin-top: 25px;
            }

            .contact-info {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
                padding-bottom: 15px;
            }

            .contact-info div {
                justify-content: flex-start;
            }

            .skills-list {
                flex-direction: column;
                gap: 15px;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 10px;
            }
            .container {
                padding: 15px;
            }
            h1 {
                font-size: 1.8em;
            }
            h2 {
                font-size: 1.1em;
            }
            h3 {
                font-size: 1.3em;
            }
            .experience-item, .project-item {
                border-left: none;
                padding-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Manzoor Ah Chopan</h1>
            <h2>Full Stack Web Developer</h2>
            <div class="contact-info">
                <div><span class="icon">📞</span> Phone: <a href="tel:7780883346">7780883346</a></div>
                <div><span class="icon">✉</span> Email: <a href="mailto:<EMAIL>"><EMAIL></a></div>
                <div><span class="icon">📍</span> Address: Srinagar, Jammu & Kashmir</div>
                <div><span class="icon">🔗</span> LinkedIn: <a href="https://linkedin.com/in/manzoor-chopan-074b14201" target="_blank" rel="noopener noreferrer">linkedin.com/in/manzoor-chopan-074b14201</a></div>
                <div><span class="icon">🔗</span> GitHub: <a href="https://github.com/mchopan" target="_blank" rel="noopener noreferrer">github.com/mchopan</a></div>
                <div><span class="icon">🔗</span> Portfolio: <a href="https://mchopan.github.io/portfolio" target="_blank" rel="noopener noreferrer">mchopan.github.io/portfolio</a></div>
            </div>
        </header>

        <section id="summary">
            <h3>Professional Summary</h3>
            <p>Full Stack Web Developer with experience in designing and developing web applications using modern technologies. Proficient in front-end and back-end development, with a focus on creating user-friendly interfaces and scalable solutions. Adept at collaborating with cross-functional teams to deliver impactful digital experiences.</p>
        </section>

        <section id="experience">
            <h3>Professional Experience</h3>

            <div class="experience-item">
                <h4>Cogveel Technologies</h4>
                <span class="role-dates">Full Stack Developer | Sep 2023 – Present</span>
                <ul>
                    <li>Developed and maintained web applications using React, Node.js, and MongoDB.</li>
                    <li>Built cross-platform mobile applications using React Native.</li>
                    <li>Collaborated with teams to design and implement new features.</li>
                    <li>Enhanced application performance and user experience.</li>
                </ul>
                <h4>Key Projects:</h4>
                <ul>
                    <li><strong>Neuquip:</strong> Solely developed the frontend of this AI-powered application with features including:
                        <ul>
                            <li>Uploading files (PDF, Excel, TXT, Word) and enabling conversational interaction with document content.</li>
                            <li>Generating analytics and exporting them into a sketchbook for document creation.</li>
                            <li>Creating workflows for sketchbooks, with sharing functionality among multiple users.</li>
                            <li>Designing an enhanced user interface to support complex workflows.</li>
                        </ul>
                    </li>
                    <li><strong>Apple Doc:</strong> Redesigned the UI and added new features to this orchid plant treatment app.</li>
                    <li><strong>Wabi Sabi:</strong> Contributed to the development of an e-commerce platform as part of a team.</li>
                    <li><strong>Quick Load:</strong> Created a booking app for trucks from scratch, handling both backend and frontend development.</li>
                </ul>
            </div>

            <div class="experience-item">
                <h4>Red Stag Labs</h4>
                <span class="role-dates">Full Stack Developer | Nov 2022 – May 2023</span>
                <ul>
                    <li>Designed and developed a dynamic full-stack web application using ReactJS, Material-UI, CSS, Spring Boot, and MySQL.</li>
                    <li>Focused on creating user-friendly interfaces and enhancing interactivity.</li>
                    <li>Contributed to the project <a href="https://marvelminds.in" target="_blank" rel="noopener noreferrer">marvelminds.in</a>.</li>
                </ul>
            </div>

            <div class="experience-item">
                <h4>Oxford Skill and Education Institute</h4>
                <span class="role-dates">Web Developer | Feb 2022 – Mar 2022</span>
                <ul>
                    <li>Developed a comprehensive registration platform for students.</li>
                    <li>Improved the institute's online presence and streamlined the registration process.</li>
                </ul>
            </div>
        </section>

        <section id="projects">
            <h3>Personal Projects</h3>
            <ul>
                <li><strong>site-bot (NPM Package):</strong> Developed a highly customizable chatbot as an npm package designed to work seamlessly with any JavaScript framework or library.
                    <ul>
                        <li>AI Integration: Utilizes an AI API key provided by the library user, enabling dynamic and context-aware interactions.</li>
                        <li>Customizable and Flexible: Designed to be easily integrated into any website, providing developers with the flexibility to customize the chatbot's behavior and appearance.</li>
                        <li>Cross-Platform Compatibility: Built with compatibility across modern JavaScript frameworks, making it adaptable for diverse project requirements.</li>
                        <li>Developer-Friendly: Created with a focus on simplicity and ease of use, allowing developers to set up the chatbot with minimal configuration.</li>
                        <li>Published on NPM: The package is published on npm, making it easily accessible for public use. (search in npm : mchopan/sitebot)</li>
                    </ul>
                </li>
                <li><strong>Campus Command (React Native | Final Year Project):</strong>
                    <ul>
                        <li>Designed and developed a messaging app tailored for educational institutions.</li>
                        <li>Enabled colleges to create semesters/groups and manage student registrations by registration numbers.</li>
                        <li>Built a student dashboard that displays only relevant semester/group data.</li>
                        <li>Integrated features like group chat, updates, and collaboration tools.</li>
                        <li>Solely responsible for the app's design, development, and deployment.</li>
                    </ul>
                </li>
                <li><strong>Portfolio Website:</strong> Showcased professional skills and projects.</li>
                <li><strong>CASET College Website:</strong> Built a dynamic website for the college.</li>
                <li><strong>Mehndi Artist Portfolio:</strong> Designed an elegant portfolio for a mehndi artist.</li>
                <li><strong>News Website:</strong> Developed a news website leveraging the News API.</li>
                <li><strong>Facebook Clone:</strong> Built a front-end clone of Facebook.</li>
                <li><strong>Itinerary Website:</strong> Created a website for booking travel-related services.</li>
            </ul>
        </section>

        <section id="education">
            <h3>Education</h3>
            <ul>
                <li><strong>Caset College of Computer Science</strong><br>Bachelor of Computer Applications (BCA) | 2021 – 2023</li>
                <li><strong>Govt Boys Higher Secondary (JKBOSE)</strong> | 2019 – 2020</li>
                <li><strong>Maulana Azad Education Foundation</strong><br>Web Designing & Publishing Assistant | 2017 – 2018</li>
                <li><strong>Govt Boys High School (JKBOSE)</strong> | 2016 – 2017</li>
            </ul>
        </section>

        <section id="skills">
            <h3>Skills</h3>
            <div class="skills-list">
                <div class="skill-category">
                    <strong>Front-End:</strong>
                    <span>React, HTML, CSS, JavaScript, Material-UI</span>
                </div>
                <div class="skill-category">
                    <strong>Back-End:</strong>
                    <span>Node.js, Spring Boot, MySQL, MongoDB</span>
                </div>
                <div class="skill-category">
                    <strong>Mobile Development:</strong>
                    <span>React Native</span>
                </div>
                <div class="skill-category">
                    <strong>Other:</strong>
                    <span>Git, Web Designing & Publishing</span>
                </div>
            </div>
        </section>

        <section id="certifications">
            <h3>Certifications</h3>
            <ul>
                <li>Full Stack Web Development – Red Stag Labs, Qamarwari, Srinagar</li>
                <li>Linux Fundamentals – Kimo.ai</li>
                <li>Hands-On Linux System Administration – Kimo.ai</li>
                <li>Data Structures and Algorithms – Kimo.ai</li>
            </ul>
        </section>
    </div>
</body>
</html>