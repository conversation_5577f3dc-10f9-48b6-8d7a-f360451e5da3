
--- 2025-07-17T03:58:44.145Z debug
🪵  Writing logs to "/home/<USER>/workspace/.config/.wrangler/logs/wrangler-2025-07-17_03-58-43_943.log"
---

--- 2025-07-17T03:58:44.145Z debug
Metrics dispatcher: Posting data {"deviceId":"94cc17b3-0f80-4847-8c99-9fee1bd4e703","event":"wrangler command started","timestamp":1752724724145,"properties":{"amplitude_session_id":*************,"amplitude_event_id":0,"wranglerVersion":"4.24.4","osPlatform":"Linux","osVersion":"#1-NixOS SMP PREEMPT_DYNAMIC Tue Jan  1 00:00:00 UTC 1980","nodeVersion":20,"packageManager":"npm","isFirstUsage":false,"configFileType":"none","isCI":false,"isPagesCI":false,"isWorkersCI":false,"isInteractive":false,"hasAssets":false,"argsUsed":["j","projectName"],"argsCombination":"j, projectName","command":"wrangler pages deploy","args":{"projectName":"<REDACTED>","xJsonConfig":true,"j":true,"uploadSourceMaps":false,"directory":"<REDACTED>"}}}
---

--- 2025-07-17T03:58:44.156Z debug
.env file not found at ".env". Continuing... For more details, refer to https://developers.cloudflare.com/workers/wrangler/system-environment-variables/
---

--- 2025-07-17T03:58:44.673Z log

 ⛅️ wrangler 4.24.4
───────────────────
---

--- 2025-07-17T03:58:44.678Z debug
Saving to cache: {"account":{"id":"<EMAIL>","name":""}}
---

--- 2025-07-17T03:58:44.680Z debug
-- START CF API REQUEST: GET https://api.cloudflare.com/client/v4/accounts/<EMAIL>/pages/projects/ai-website-51c42b80
---

--- 2025-07-17T03:58:44.681Z debug
QUERY STRING: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T03:58:44.681Z debug
HEADERS: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T03:58:44.681Z debug
INIT: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T03:58:44.681Z debug
-- END CF API REQUEST
---

--- 2025-07-17T03:58:44.784Z debug
-- START CF API RESPONSE: Not found 404
---

--- 2025-07-17T03:58:44.785Z debug
HEADERS: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T03:58:44.785Z debug
RESPONSE: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T03:58:44.785Z debug
-- END CF API RESPONSE
---

--- 2025-07-17T03:58:44.787Z log

---

--- 2025-07-17T03:58:44.845Z log
[31m✘ [41;31m[[41;97mERROR[41;31m][0m [1mA request to the Cloudflare API (/accounts/<EMAIL>/pages/projects/ai-website-51c42b80) failed.[0m

  Could not route to /client/v4/accounts/<EMAIL>/pages/projects/ai-website-51c42b80, perhaps your object identifier is invalid? [code: 7003]
  
  If you think this is a bug, please open an issue at: [4mhttps://github.com/cloudflare/workers-sdk/issues/new/choose[0m


---

--- 2025-07-17T03:58:44.846Z debug
Metrics dispatcher: Posting data {"deviceId":"94cc17b3-0f80-4847-8c99-9fee1bd4e703","event":"wrangler command errored","timestamp":*************,"properties":{"amplitude_session_id":*************,"amplitude_event_id":1,"wranglerVersion":"4.24.4","osPlatform":"Linux","osVersion":"#1-NixOS SMP PREEMPT_DYNAMIC Tue Jan  1 00:00:00 UTC 1980","nodeVersion":20,"packageManager":"npm","isFirstUsage":false,"configFileType":"none","isCI":false,"isPagesCI":false,"isWorkersCI":false,"isInteractive":false,"hasAssets":false,"argsUsed":["j","projectName"],"argsCombination":"j, projectName","command":"wrangler pages deploy","args":{"projectName":"<REDACTED>","xJsonConfig":true,"j":true,"uploadSourceMaps":false,"directory":"<REDACTED>"},"durationMs":729,"durationSeconds":0.729,"durationMinutes":0.01215,"errorType":"APIError"}}
---
