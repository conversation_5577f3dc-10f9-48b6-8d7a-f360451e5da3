
--- 2025-07-17T04:26:32.990Z debug
🪵  Writing logs to "/home/<USER>/workspace/.config/.wrangler/logs/wrangler-2025-07-17_04-26-32_784.log"
---

--- 2025-07-17T04:26:32.990Z debug
Metrics dispatcher: Posting data {"deviceId":"94cc17b3-0f80-4847-8c99-9fee1bd4e703","event":"wrangler command started","timestamp":1752726392990,"properties":{"amplitude_session_id":1752726392988,"amplitude_event_id":0,"wranglerVersion":"4.24.4","osPlatform":"Linux","osVersion":"#1-NixOS SMP PREEMPT_DYNAMIC Tue Jan  1 00:00:00 UTC 1980","nodeVersion":20,"packageManager":"npm","isFirstUsage":false,"configFileType":"none","isCI":false,"isPagesCI":false,"isWorkersCI":false,"isInteractive":false,"hasAssets":false,"argsUsed":["j","projectName"],"argsCombination":"j, projectName","command":"wrangler pages deploy","args":{"projectName":"<REDACTED>","xJsonConfig":true,"j":true,"uploadSourceMaps":false,"directory":"<REDACTED>"}}}
---

--- 2025-07-17T04:26:33.012Z debug
.env file not found at ".env". Continuing... For more details, refer to https://developers.cloudflare.com/workers/wrangler/system-environment-variables/
---

--- 2025-07-17T04:26:33.054Z log

 ⛅️ wrangler 4.24.4
───────────────────
---

--- 2025-07-17T04:26:33.058Z debug
Saving to cache: {"account":{"id":"dca21f9606d1acfc7b48277103239f40","name":""}}
---

--- 2025-07-17T04:26:33.060Z debug
-- START CF API REQUEST: GET https://api.cloudflare.com/client/v4/accounts/dca21f9606d1acfc7b48277103239f40/pages/projects/ai-website-5b9283dc
---

--- 2025-07-17T04:26:33.060Z debug
QUERY STRING: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T04:26:33.060Z debug
HEADERS: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T04:26:33.060Z debug
INIT: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T04:26:33.060Z debug
-- END CF API REQUEST
---

--- 2025-07-17T04:26:33.362Z debug
-- START CF API RESPONSE: Not Found 404
---

--- 2025-07-17T04:26:33.363Z debug
HEADERS: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T04:26:33.363Z debug
RESPONSE: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T04:26:33.363Z debug
-- END CF API RESPONSE
---

--- 2025-07-17T04:26:33.365Z log

---

--- 2025-07-17T04:26:33.416Z error
[31m✘ [41;31m[[41;97mERROR[41;31m][0m [1mThis command cannot be run in a non-interactive context[0m


---

--- 2025-07-17T04:26:33.416Z debug
Error: This command cannot be run in a non-interactive context
    at select (/home/<USER>/workspace/node_modules/wrangler/wrangler-dist/cli.js:52807:13)
    at promptSelectExistingOrNewProject (/home/<USER>/workspace/node_modules/wrangler/wrangler-dist/cli.js:92629:10)
    at Object.handler (/home/<USER>/workspace/node_modules/wrangler/wrangler-dist/cli.js:92831:35)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Object.handler (/home/<USER>/workspace/node_modules/wrangler/wrangler-dist/cli.js:82454:7)
---

--- 2025-07-17T04:26:33.417Z debug
Metrics dispatcher: Posting data {"deviceId":"94cc17b3-0f80-4847-8c99-9fee1bd4e703","event":"wrangler command errored","timestamp":1752726393417,"properties":{"amplitude_session_id":1752726392988,"amplitude_event_id":1,"wranglerVersion":"4.24.4","osPlatform":"Linux","osVersion":"#1-NixOS SMP PREEMPT_DYNAMIC Tue Jan  1 00:00:00 UTC 1980","nodeVersion":20,"packageManager":"npm","isFirstUsage":false,"configFileType":"none","isCI":false,"isPagesCI":false,"isWorkersCI":false,"isInteractive":false,"hasAssets":false,"argsUsed":["j","projectName"],"argsCombination":"j, projectName","command":"wrangler pages deploy","args":{"projectName":"<REDACTED>","xJsonConfig":true,"j":true,"uploadSourceMaps":false,"directory":"<REDACTED>"},"durationMs":453,"durationSeconds":0.453,"durationMinutes":0.00755,"errorType":"NoDefaultValueProvided","errorMessage":"This command cannot be run in a non-interactive context"}}
---
