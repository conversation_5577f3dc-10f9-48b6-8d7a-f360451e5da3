
--- 2025-07-17T04:01:39.485Z debug
🪵  Writing logs to "/home/<USER>/workspace/.config/.wrangler/logs/wrangler-2025-07-17_04-01-39_330.log"
---

--- 2025-07-17T04:01:39.485Z debug
Metrics dispatcher: Posting data {"deviceId":"94cc17b3-0f80-4847-8c99-9fee1bd4e703","event":"wrangler command started","timestamp":*************,"properties":{"amplitude_session_id":*************,"amplitude_event_id":0,"wranglerVersion":"4.24.4","osPlatform":"Linux","osVersion":"#1-NixOS SMP PREEMPT_DYNAMIC Tue Jan  1 00:00:00 UTC 1980","nodeVersion":20,"packageManager":"npm","isFirstUsage":false,"configFileType":"none","isCI":false,"isPagesCI":false,"isWorkersCI":false,"isInteractive":false,"hasAssets":false,"argsUsed":["accountId","j","projectName"],"argsCombination":"accountId, j, projectName","command":"wrangler pages deploy","args":{"projectName":"<REDACTED>","accountId":"<REDACTED>","xJsonConfig":true,"j":true,"uploadSourceMaps":false,"directory":"<REDACTED>"}}}
---

--- 2025-07-17T04:01:39.496Z log

---

--- 2025-07-17T04:01:39.536Z error
[31m✘ [41;31m[[41;97mERROR[41;31m][0m [1mUnknown arguments: account-id, accountId[0m


---

--- 2025-07-17T04:01:39.565Z debug
Metrics dispatcher: Posting data {"deviceId":"94cc17b3-0f80-4847-8c99-9fee1bd4e703","event":"wrangler command errored","timestamp":*************,"properties":{"amplitude_session_id":*************,"amplitude_event_id":1,"wranglerVersion":"4.24.4","osPlatform":"Linux","osVersion":"#1-NixOS SMP PREEMPT_DYNAMIC Tue Jan  1 00:00:00 UTC 1980","nodeVersion":20,"packageManager":"npm","isFirstUsage":false,"configFileType":"none","isCI":false,"isPagesCI":false,"isWorkersCI":false,"isInteractive":false,"hasAssets":false,"argsUsed":["accountId","j","projectName"],"argsCombination":"accountId, j, projectName","command":"wrangler pages deploy","args":{"projectName":"<REDACTED>","accountId":"<REDACTED>","xJsonConfig":true,"j":true,"uploadSourceMaps":false,"directory":"<REDACTED>"},"durationMs":109,"durationSeconds":0.109,"durationMinutes":0.0018166666666666667,"errorType":"CommandLineArgsError","errorMessage":"yargs validation error"}}
---
