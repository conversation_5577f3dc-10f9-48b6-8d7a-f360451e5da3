import os
import uuid
import json
from datetime import datetime
from typing import Dict, Any

class SimpleFileDeployment:
    """Simple file-based deployment system"""
    
    def __init__(self):
        self.base_dir = "deployed-websites"
        os.makedirs(self.base_dir, exist_ok=True)
    
    def deploy_website(self, html_content: str, name_hint: str = "") -> str:
        """Deploy HTML content to local file system with mock URL"""
        try:
            # Generate unique deployment ID
            deployment_id = str(uuid.uuid4())[:8]
            
            # Create project name
            if name_hint:
                project_name = f"website-{name_hint.lower().replace(' ', '-')}-{deployment_id}"
            else:
                project_name = f"ai-website-{deployment_id}"
            
            # Create deployment directory
            deploy_dir = os.path.join(self.base_dir, project_name)
            os.makedirs(deploy_dir, exist_ok=True)
            
            # Write HTML file
            html_path = os.path.join(deploy_dir, "index.html")
            with open(html_path, "w", encoding="utf-8") as f:
                f.write(html_content)
            
            # Generate mock Cloudflare-style URL
            mock_url = f"https://{project_name}.pages.dev"
            
            # Save deployment record
            deployment_record = {
                "deployment_id": deployment_id,
                "project_name": project_name,
                "url": mock_url,
                "local_path": deploy_dir,
                "created_at": datetime.now().isoformat(),
                "status": "deployed",
                "html_size": len(html_content)
            }
            self._save_deployment_record(deployment_record)
            
            return f"""✅ Website deployed successfully!
🌐 Mock URL: {mock_url}
📁 Local file: {html_path}
📝 Project: {project_name}

Note: Due to Cloudflare authentication requirements in this environment, 
your website has been saved locally. In a production environment, 
this would be deployed to a live Cloudflare Pages URL."""
            
        except Exception as e:
            return f"❌ Deployment error: {str(e)}"
    
    def _save_deployment_record(self, deployment_record: Dict[str, Any]):
        """Save deployment record to local file"""
        try:
            deployments_file = "deployments_history.json"
            
            # Load existing deployments
            if os.path.exists(deployments_file):
                with open(deployments_file, 'r') as f:
                    deployments = json.load(f)
            else:
                deployments = []
            
            # Add new deployment
            deployments.append(deployment_record)
            
            # Save updated deployments
            with open(deployments_file, 'w') as f:
                json.dump(deployments, f, indent=2)
                
        except Exception as e:
            print(f"Warning: Could not save deployment record: {str(e)}")
    
    def get_deployment_history(self) -> list:
        """Get deployment history"""
        try:
            deployments_file = "deployments_history.json"
            
            if os.path.exists(deployments_file):
                with open(deployments_file, 'r') as f:
                    return json.load(f)
            else:
                return []
                
        except Exception:
            return []

def deploy_locally(html_content: str, name_hint: str = "") -> str:
    """Deploy HTML content locally with mock Cloudflare URL"""
    deployment = SimpleFileDeployment()
    return deployment.deploy_website(html_content, name_hint)