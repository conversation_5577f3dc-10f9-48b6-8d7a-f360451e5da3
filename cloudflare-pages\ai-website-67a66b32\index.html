<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - Full Stack Web Developer</title>
    <style>
        /* General Body Styles */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8fafc; /* Background color */
            color: #1e293b; /* Text color */
            line-height: 1.6;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* Container for the whole resume */
        .container {
            max-width: 900px;
            margin: 30px auto;
            padding: 20px;
            background-color: #ffffff; /* White background for the content area */
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            border-radius: 8px;
        }

        /* Header/Hero Section */
        .hero-section {
            text-align: center;
            padding-bottom: 25px;
            border-bottom: 2px solid #e2e8f0; /* Light border */
            margin-bottom: 30px;
        }

        .hero-section h1 {
            color: #2563eb; /* Primary color for name */
            font-size: 2.8em;
            margin-bottom: 5px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .hero-section .title {
            font-size: 1.4em;
            color: #334155; /* Slightly lighter text for title */
            margin-top: 0;
            margin-bottom: 20px;
        }

        .contact-info {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px 30px; /* Gap between contact items */
            margin-top: 20px;
        }

        .contact-info p {
            margin: 0;
            display: flex;
            align-items: center;
            font-size: 0.95em;
        }

        .contact-info p span {
            font-weight: 600;
            margin-right: 8px;
            min-width: 60px; /* Align labels */
            text-align: right;
        }

        .contact-info a {
            color: #1e293b; /* Text color for links */
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .contact-info a:hover {
            color: #2563eb; /* Primary color on hover */
            text-decoration: underline;
        }

        /* Section Styling */
        .section {
            margin-bottom: 30px;
            padding-top: 10px;
        }

        .section h2 {
            color: #2563eb; /* Primary color for section headings */
            font-size: 2em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #2563eb; /* Underline for section headings */
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Sub-headings for experience, projects, education */
        h3 {
            color: #2563eb; /* Primary color for sub-headings */
            font-size: 1.3em;
            margin-bottom: 5px;
            margin-top: 25px;
        }

        h4 {
            color: #334155;
            font-size: 1.1em;
            margin-top: 15px;
            margin-bottom: 10px;
        }

        /* Paragraphs */
        p {
            margin-bottom: 10px;
        }

        /* List Styling */
        ul {
            list-style: none; /* Remove default bullet */
            padding: 0;
            margin: 0;
        }

        ul li {
            position: relative;
            padding-left: 25px; /* Space for custom bullet */
            margin-bottom: 8px;
        }

        ul li::before {
            content: '•'; /* Custom bullet point */
            color: #2563eb; /* Primary color for bullet */
            font-size: 1.2em;
            position: absolute;
            left: 0;
            top: 0;
        }

        .projects-list ul { /* Nested lists for projects */
            list-style: none;
            padding-left: 20px;
        }

        .projects-list ul li::before {
            content: '-'; /* Different bullet for nested lists */
            color: #334155;
            font-size: 1em;
            left: 0;
        }


        /* Specific Section Item Styles */
        .experience-item,
        .project-item,
        .education-item {
            margin-bottom: 25px;
            padding-left: 15px;
            border-left: 3px solid #cbd5e1; /* Subtle left border */
        }

        .experience-item:last-child,
        .project-item:last-child,
        .education-item:last-child {
            margin-bottom: 0; /* No margin for the last item in a section */
        }

        .duration {
            font-style: italic;
            color: #64748b; /* Gray color for dates */
            margin-top: -5px;
            margin-bottom: 15px;
            display: block; /* Ensure it takes full width */
        }

        /* Skills Section */
        .skills-list {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }

        .skill-category {
            flex: 1 1 calc(50% - 10px); /* Two columns, with gap */
            min-width: 250px; /* Minimum width before wrapping */
        }

        .skill-category h3 {
            color: #334155; /* Darker sub-heading for skill categories */
            font-size: 1.1em;
            margin-top: 0;
            margin-bottom: 5px;
        }

        .skill-category p {
            background-color: #eff6ff; /* Light blue background for skill text */
            padding: 8px 12px;
            border-radius: 5px;
            display: inline-block; /* To make background fit content */
            margin-right: 5px;
            margin-bottom: 5px;
            font-size: 0.9em;
            color: #2563eb; /* Primary color for skill text */
            border: 1px solid #bfdbfe;
        }


        /* Responsive adjustments */
        @media (max-width: 768px) {
            .container {
                margin: 20px auto;
                padding: 15px;
            }

            .hero-section h1 {
                font-size: 2.2em;
            }

            .hero-section .title {
                font-size: 1.2em;
            }

            .contact-info {
                flex-direction: column;
                align-items: center;
                gap: 10px;
            }

            .contact-info p span {
                min-width: unset; /* Remove fixed width on smaller screens */
                text-align: left;
            }

            .section h2 {
                font-size: 1.8em;
            }

            h3 {
                font-size: 1.2em;
            }

            .skills-list {
                flex-direction: column; /* Stack skills categories */
                gap: 15px;
            }

            .skill-category {
                flex: 1 1 100%; /* Full width on small screens */
            }
        }

        @media (max-width: 480px) {
            .hero-section h1 {
                font-size: 1.8em;
            }
            .hero-section .title {
                font-size: 1em;
            }
            .section h2 {
                font-size: 1.5em;
            }
            h3 {
                font-size: 1.1em;
            }
            ul li {
                padding-left: 20px;
            }
            ul li::before {
                font-size: 1em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="hero-section">
            <h1>Manzoor Ah Chopan</h1>
            <p class="title">Full Stack Web Developer</p>
            <div class="contact-info">
                <p>📞 <span>Phone:</span> <a href="tel:7780883346">7780883346</a></p>
                <p>✉ <span>Email:</span> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                <p>📍 <span>Address:</span> Srinagar, Jammu & Kashmir</p>
                <p>🔗 <span>LinkedIn:</span> <a href="https://linkedin.com/in/manzoor-chopan-074b14201" target="_blank" rel="noopener noreferrer">linkedin.com/in/manzoor-chopan-074b14201</a></p>
                <p>🔗 <span>GitHub:</span> <a href="https://github.com/mchopan" target="_blank" rel="noopener noreferrer">github.com/mchopan</a></p>
                <p>🔗 <span>Portfolio:</span> <a href="https://mchopan.github.io/portfolio" target="_blank" rel="noopener noreferrer">mchopan.github.io/portfolio</a></p>
            </div>
        </header>

        <section class="section">
            <h2>Professional Summary</h2>
            <p>Full Stack Web Developer with experience in designing and developing web applications using modern technologies. Proficient in front-end and back-end development, with a focus on creating user-friendly interfaces and scalable solutions. Adept at collaborating with cross-functional teams to deliver impactful digital experiences.</p>
        </section>

        <section class="section">
            <h2>Professional Experience</h2>
            <div class="experience-item">
                <h3>Cogveel Technologies | Full Stack Developer</h3>
                <p class="duration">Sep 2023 – Present</p>
                <ul>
                    <li>Developed and maintained web applications using React, Node.js, and MongoDB.</li>
                    <li>Built cross-platform mobile applications using React Native.</li>
                    <li>Collaborated with teams to design and implement new features.</li>
                    <li>Enhanced application performance and user experience.</li>
                </ul>
                <h4>Key Projects:</h4>
                <ul class="projects-list">
                    <li><strong>Neuquip:</strong> Solely developed the frontend of this AI-powered application with features including:
                        <ul>
                            <li>Uploading files (PDF, Excel, TXT, Word) and enabling conversational interaction with document content.</li>
                            <li>Generating analytics and exporting them into a sketchbook for document creation.</li>
                            <li>Creating workflows for sketchbooks, with sharing functionality among multiple users.</li>
                            <li>Designing an enhanced user interface to support complex workflows.</li>
                        </ul>
                    </li>
                    <li><strong>Apple Doc:</strong> Redesigned the UI and added new features to this orchid plant treatment app.</li>
                    <li><strong>Wabi Sabi:</strong> Contributed to the development of an e-commerce platform as part of a team.</li>
                    <li><strong>Quick Load:</strong> Created a booking app for trucks from scratch, handling both backend and frontend development.</li>
                </ul>
            </div>

            <div class="experience-item">
                <h3>Red Stag Labs | Full Stack Developer</h3>
                <p class="duration">Nov 2022 – May 2023</p>
                <ul>
                    <li>Designed and developed a dynamic full-stack web application using ReactJS, Material-UI, CSS, Spring Boot, and MySQL.</li>
                    <li>Focused on creating user-friendly interfaces and enhancing interactivity.</li>
                    <li>Contributed to the project marvelminds.in.</li>
                </ul>
            </div>

            <div class="experience-item">
                <h3>Oxford Skill and Education Institute | Web Developer</h3>
                <p class="duration">Feb 2022 – Mar 2022</p>
                <ul>
                    <li>Developed a comprehensive registration platform for students.</li>
                    <li>Improved the institute's online presence and streamlined the registration process.</li>
                </ul>
            </div>
        </section>

        <section class="section">
            <h2>Personal Projects</h2>
            <div class="project-item">
                <h3>site-bot (NPM Package)</h3>
                <ul>
                    <li>Developed a highly customizable chatbot as an npm package designed to work seamlessly with any JavaScript framework or library.</li>
                    <li><strong>AI Integration:</strong> Utilizes an AI API key provided by the library user, enabling dynamic and context-aware interactions.</li>
                    <li><strong>Customizable and Flexible:</strong> Designed to be easily integrated into any website, providing developers with the flexibility to customize the chatbot's behavior and appearance.</li>
                    <li><strong>Cross-Platform Compatibility:</strong> Built with compatibility across modern JavaScript frameworks, making it adaptable for diverse project requirements.</li>
                    <li><strong>Developer-Friendly:</strong> Created with a focus on simplicity and ease of use, allowing developers to set up the chatbot with minimal configuration.</li>
                    <li><strong>Published on NPM:</strong> The package is published on npm, making it easily accessible for public use. (search in npm : mchopan/sitebot)</li>
                </ul>
            </div>
            <div class="project-item">
                <h3>Campus Command (React Native | Final Year Project)</h3>
                <ul>
                    <li>Designed and developed a messaging app tailored for educational institutions.</li>
                    <li>Enabled colleges to create semesters/groups and manage student registrations by registration numbers.</li>
                    <li>Built a student dashboard that displays only relevant semester/group data.</li>
                    <li>Integrated features like group chat, updates, and collaboration tools.</li>
                    <li>Solely responsible for the app's design, development, and deployment.</li>
                </ul>
            </div>
            <div class="project-item">
                <h3>Portfolio Website</h3>
                <ul>
                    <li>Showcased professional skills and projects.</li>
                </ul>
            </div>
            <div class="project-item">
                <h3>CASET College Website</h3>
                <ul>
                    <li>Built a dynamic website for the college.</li>
                </ul>
            </div>
            <div class="project-item">
                <h3>Mehndi Artist Portfolio</h3>
                <ul>
                    <li>Designed an elegant portfolio for a mehndi artist.</li>
                </ul>
            </div>
            <div class="project-item">
                <h3>News Website</h3>
                <ul>
                    <li>Developed a news website leveraging the News API.</li>
                </ul>
            </div>
            <div class="project-item">
                <h3>Facebook Clone</h3>
                <ul>
                    <li>Built a front-end clone of Facebook.</li>
                </ul>
            </div>
            <div class="project-item">
                <h3>Itinerary Website</h3>
                <ul>
                    <li>Created a website for booking travel-related services.</li>
                </ul>
            </div>
        </section>

        <section class="section">
            <h2>Education</h2>
            <div class="education-item">
                <h3>Caset College of Computer Science</h3>
                <p>Bachelor of Computer Applications (BCA) | 2021 – 2023</p>
            </div>
            <div class="education-item">
                <h3>Govt Boys Higher Secondary (JKBOSE)</h3>
                <p>2019 – 2020</p>
            </div>
            <div class="education-item">
                <h3>Maulana Azad Education Foundation</h3>
                <p>Web Designing & Publishing Assistant | 2017 – 2018</p>
            </div>
            <div class="education-item">
                <h3>Govt Boys High School (JKBOSE)</h3>
                <p>2016 – 2017</p>
            </div>
        </section>

        <section class="section">
            <h2>Skills</h2>
            <div class="skills-list">
                <div class="skill-category">
                    <h3>Front-End:</h3>
                    <p>React, HTML, CSS, JavaScript, Material-UI</p>
                </div>
                <div class="skill-category">
                    <h3>Back-End:</h3>
                    <p>Node.js, Spring Boot, MySQL, MongoDB</p>
                </div>
                <div class="skill-category">
                    <h3>Mobile Development:</h3>
                    <p>React Native</p>
                </div>
                <div class="skill-category">
                    <h3>Other:</h3>
                    <p>Git, Web Designing & Publishing</p>
                </div>
            </div>
        </section>

        <section class="section">
            <h2>Certifications</h2>
            <ul>
                <li>Full Stack Web Development – Red Stag Labs, Qamarwari, Srinagar</li>
                <li>Linux Fundamentals – Kimo.ai</li>
                <li>Hands-On Linux System Administration – Kimo.ai</li>
                <li>Data Structures and Algorithms – Kimo.ai</li>
            </ul>
        </section>
    </div>
</body>
</html>