import streamlit as st
import os
import tempfile
from simple_agent import get_simple_agent

def main():
    st.set_page_config(
        page_title="AI Website Generator - Agentic Mode",
        page_icon="🤖",
        layout="wide"
    )
    
    st.title("🤖 AI Website Generator - Agentic Mode")
    st.markdown("Chat with your personal AI web developer to create professional websites")
    
    # Initialize session state
    if 'agent' not in st.session_state:
        st.session_state.agent = get_simple_agent()
    if 'messages' not in st.session_state:
        st.session_state.messages = []
    
    # Sidebar for configuration
    with st.sidebar:
        st.header("⚙️ Configuration")
        api_key = st.text_input(
            "Google Gemini API Key",
            type="password",
            value=os.getenv("GEMINI_API_KEY", ""),
            help="Enter your Google Gemini API key"
        )
        
        if api_key:
            os.environ["GEMINI_API_KEY"] = api_key
            st.success("✅ API Key configured")
        else:
            st.warning("⚠️ Please enter your Gemini API key to continue")
            return
        
        st.markdown("---")
        st.subheader("🎯 Quick Actions")
        
        # File upload in sidebar
        uploaded_file = st.file_uploader(
            "Upload Resume/CV or Instructions",
            type=['pdf', 'txt', 'docx'],
            help="Optional: Upload a file to process"
        )
        
        if uploaded_file is not None:
            # Save uploaded file temporarily
            with tempfile.NamedTemporaryFile(delete=False, suffix=f".{uploaded_file.name.split('.')[-1]}") as tmp_file:
                tmp_file.write(uploaded_file.getvalue())
                tmp_file_path = tmp_file.name
            
            if st.button("📤 Send File to Agent"):
                # Process file and send to agent
                file_type = "resume" if "resume" in uploaded_file.name.lower() or "cv" in uploaded_file.name.lower() else "instructions"
                
                # Process message with agent
                with st.spinner("Processing file with agent..."):
                    try:
                        # Process file directly
                        agent_response = st.session_state.agent.process_file(tmp_file_path, file_type)
                        
                        # Add messages to chat
                        st.session_state.messages.append({
                            "role": "user",
                            "content": f"📎 Uploaded file: {uploaded_file.name}"
                        })
                        
                        st.session_state.messages.append({
                            "role": "assistant", 
                            "content": agent_response
                        })
                        
                        st.success("File processed successfully!")
                        st.rerun()
                        
                    except Exception as e:
                        st.error(f"Error processing file: {str(e)}")
                    finally:
                        # Clean up temporary file
                        if os.path.exists(tmp_file_path):
                            os.unlink(tmp_file_path)
        
        if st.button("🔄 Reset Conversation"):
            st.session_state.agent.reset()
            st.session_state.messages = []
            st.success("Conversation reset!")
            st.rerun()
    
    # Main content area
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.header("💬 Chat with AI Agent")
        
        # Display chat messages
        chat_container = st.container()
        
        with chat_container:
            # Initial greeting if no messages
            if not st.session_state.messages:
                st.markdown("""
                **🤖 AI Web Developer:** Hello! I'm your personal web developer. I can help you create beautiful websites in several ways:
                
                1. **📄 Upload your resume/CV** - I'll create a professional portfolio
                2. **📋 Upload instruction files** - I'll follow your specific requirements  
                3. **💬 Just describe what you want** - Tell me about your ideal website
                
                What would you like to do today?
                """)
            
            # Display conversation history
            for msg in st.session_state.messages:
                if msg["role"] == "user":
                    st.markdown(f"**👤 You:** {msg['content']}")
                else:
                    st.markdown(f"**🤖 AI Agent:** {msg['content']}")
        
        # Chat input
        with st.form("chat_form", clear_on_submit=True):
            user_input = st.text_area(
                "Type your message:",
                height=100,
                placeholder="Ask me to create a website, upload files, or describe what you want..."
            )
            
            col_submit, col_example = st.columns([1, 1])
            
            with col_submit:
                submit_button = st.form_submit_button("💬 Send Message")
            
            with col_example:
                if st.form_submit_button("💡 Example Request"):
                    user_input = "Create a modern portfolio website for a software developer with dark theme"
                    submit_button = True
        
        if submit_button and user_input.strip():
            # Add user message to chat
            st.session_state.messages.append({
                "role": "user",
                "content": user_input
            })
            
            # Process message with agent
            with st.spinner("AI Agent is thinking..."):
                try:
                    agent_response = st.session_state.agent.process_message(user_input)
                    
                    st.session_state.messages.append({
                        "role": "assistant",
                        "content": agent_response
                    })
                    
                    st.rerun()
                    
                except Exception as e:
                    st.error(f"Error: {str(e)}")
                    st.session_state.messages.append({
                        "role": "assistant",
                        "content": f"❌ I encountered an error: {str(e)}. Please try again."
                    })
    
    with col2:
        st.header("👁️ Website Preview")
        
        # Get agent state
        agent_state = st.session_state.agent.get_state()
        current_html = agent_state.get("html_content", "")
        
        if current_html:
            # Website preview tabs
            preview_tabs = st.tabs(["🌐 Live Preview", "📝 HTML Code", "🚀 Deploy"])
            
            with preview_tabs[0]:
                st.subheader("Website Preview")
                try:
                    # Display the generated HTML
                    st.components.v1.html(
                        current_html,
                        height=600,
                        scrolling=True
                    )
                except Exception as e:
                    st.error(f"Preview error: {str(e)}")
                    st.code(current_html, language="html")
            
            with preview_tabs[1]:
                st.subheader("HTML Code")
                st.code(current_html, language="html")
                
                # Download button
                st.download_button(
                    label="📥 Download HTML",
                    data=current_html,
                    file_name="ai_generated_website.html",
                    mime="text/html"
                )
            
            with preview_tabs[2]:
                st.subheader("Deploy Website")
                
                # Satisfaction check
                satisfaction_form = st.form("satisfaction_form")
                with satisfaction_form:
                    st.markdown("**Are you satisfied with this website?**")
                    
                    col_yes, col_no = st.columns(2)
                    
                    with col_yes:
                        publish_button = st.form_submit_button("✅ Yes, Publish It!", type="primary")
                    
                    with col_no:
                        changes_button = st.form_submit_button("🔄 No, I Want Changes")
                
                if publish_button:
                    # Send publish confirmation to agent
                    with st.spinner("Publishing website..."):
                        try:
                            agent_response = st.session_state.agent.process_message("Yes, publish it!")
                            
                            st.session_state.messages.append({
                                "role": "user",
                                "content": "Yes, publish it!"
                            })
                            st.session_state.messages.append({
                                "role": "assistant",
                                "content": agent_response
                            })
                            
                            if "deployed successfully" in agent_response:
                                st.success("🎉 Website published successfully!")
                                st.balloons()
                            
                            st.rerun()
                            
                        except Exception as e:
                            st.error(f"Deployment error: {str(e)}")
                
                if changes_button:
                    # Send change request to agent
                    agent_response = st.session_state.agent.process_message("I want to make some changes.")
                    
                    st.session_state.messages.append({
                        "role": "user",
                        "content": "I want to make some changes to the website."
                    })
                    st.session_state.messages.append({
                        "role": "assistant",
                        "content": agent_response
                    })
                    
                    st.rerun()
                
                # Show current state info
                with st.expander("🔍 Debug Info"):
                    st.json({
                        "has_content": agent_state.get("has_content", False),
                        "website_created": agent_state.get("website_created", False),
                        "awaiting_confirmation": agent_state.get("awaiting_confirmation", False),
                        "html_content_length": len(current_html)
                    })
        
        else:
            st.info("👆 Chat with the AI agent to create a website, then it will appear here for preview and deployment.")

if __name__ == "__main__":
    main()
