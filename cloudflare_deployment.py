import os
import subprocess
import uuid
import json
from datetime import datetime
from typing import Dict, Any

class CloudflareDeployment:
    """Real Cloudflare Pages deployment using Wrangler"""
    
    def __init__(self):
        self.account_id = os.getenv("CLOUDFLARE_ACCOUNT_ID")
        self.api_token = os.getenv("CLOUDFLARE_API_TOKEN")
        self.base_dir = "cloudflare-pages"
        
        # Debug account ID
        print(f"Cloudflare Account ID: {self.account_id}")
        print(f"Account ID type: {type(self.account_id)}")
        
        self.setup_environment()
    
    def setup_environment(self):
        """Set up the deployment environment"""
        # Create base directory
        os.makedirs(self.base_dir, exist_ok=True)
        
        # Set environment variables for wrangler
        os.environ["CLOUDFLARE_ACCOUNT_ID"] = self.account_id
        os.environ["CLOUDFLARE_API_TOKEN"] = self.api_token
    
    def deploy_website(self, html_content: str, name_hint: str = "") -> str:
        """Deploy HTML content to Cloudflare Pages"""
        try:
            # Generate unique deployment ID
            deployment_id = str(uuid.uuid4())[:8]
            
            # Create project name
            if name_hint:
                project_name = f"website-{name_hint.lower().replace(' ', '-')}-{deployment_id}"
            else:
                project_name = f"ai-website-{deployment_id}"
            
            # Create deployment directory
            deploy_dir = os.path.join(self.base_dir, project_name)
            os.makedirs(deploy_dir, exist_ok=True)
            
            # Write HTML file
            html_path = os.path.join(deploy_dir, "index.html")
            with open(html_path, "w", encoding="utf-8") as f:
                f.write(html_content)
            
            # Deploy using wrangler
            result = self._deploy_with_wrangler(deploy_dir, project_name)
            
            if result["success"]:
                # Save deployment record
                deployment_record = {
                    "deployment_id": deployment_id,
                    "project_name": project_name,
                    "url": result["url"],
                    "created_at": datetime.now().isoformat(),
                    "status": "deployed",
                    "html_size": len(html_content)
                }
                self._save_deployment_record(deployment_record)
                
                return f"✅ Website deployed successfully to Cloudflare Pages!\n🌐 Your live URL: {result['url']}\n📝 Project: {project_name}"
            else:
                return f"❌ Deployment failed: {result['error']}"
                
        except Exception as e:
            return f"❌ Deployment error: {str(e)}"
    
    def _deploy_with_wrangler(self, deploy_dir: str, project_name: str) -> Dict[str, Any]:
        """Deploy using Wrangler CLI"""
        try:
            # Run wrangler pages deploy (let wrangler auto-detect account)
            cmd = [
                "npx", "wrangler", "pages", "deploy", deploy_dir,
                "--project-name", project_name
            ]
            
            # Set environment variables
            env = os.environ.copy()
            env["CLOUDFLARE_ACCOUNT_ID"] = self.account_id
            env["CLOUDFLARE_API_TOKEN"] = self.api_token
            
            result = subprocess.run(
                cmd,
                cwd=self.base_dir,
                capture_output=True,
                text=True,
                env=env,
                timeout=120  # 2 minute timeout
            )
            
            if result.returncode == 0:
                # Parse the output to extract URL
                output = result.stdout
                
                # Look for the deployment URL in the output
                url = f"https://{project_name}.pages.dev"
                if "https://" in output:
                    # Try to extract the actual URL from output
                    lines = output.split('\n')
                    for line in lines:
                        if "https://" in line and "pages.dev" in line:
                            # Extract URL from line
                            import re
                            url_match = re.search(r'https://[^\s]+\.pages\.dev', line)
                            if url_match:
                                found_url = url_match.group(0)
                                # Ensure no double .pages.dev
                                if found_url.count('.pages.dev') == 1:
                                    url = found_url
                                break
                
                return {
                    "success": True,
                    "url": url,
                    "output": output
                }
            else:
                return {
                    "success": False,
                    "error": result.stderr or result.stdout,
                    "output": result.stdout
                }
                
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "error": "Deployment timed out after 2 minutes"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def _save_deployment_record(self, deployment_record: Dict[str, Any]):
        """Save deployment record to local file"""
        try:
            deployments_file = "cloudflare_deployments.json"
            
            # Load existing deployments
            if os.path.exists(deployments_file):
                with open(deployments_file, 'r') as f:
                    deployments = json.load(f)
            else:
                deployments = []
            
            # Add new deployment
            deployments.append(deployment_record)
            
            # Save updated deployments
            with open(deployments_file, 'w') as f:
                json.dump(deployments, f, indent=2)
                
        except Exception as e:
            print(f"Warning: Could not save deployment record: {str(e)}")
    
    def get_deployment_history(self) -> list:
        """Get deployment history"""
        try:
            deployments_file = "cloudflare_deployments.json"
            
            if os.path.exists(deployments_file):
                with open(deployments_file, 'r') as f:
                    return json.load(f)
            else:
                return []
                
        except Exception:
            return []

# Singleton instance
_cloudflare_deployment = None

def get_cloudflare_deployment():
    """Get singleton Cloudflare deployment instance"""
    global _cloudflare_deployment
    if _cloudflare_deployment is None:
        _cloudflare_deployment = CloudflareDeployment()
    return _cloudflare_deployment

def deploy_to_cloudflare(html_content: str, name_hint: str = "") -> str:
    """Deploy HTML content to Cloudflare Pages"""
    deployment = get_cloudflare_deployment()
    return deployment.deploy_website(html_content, name_hint)