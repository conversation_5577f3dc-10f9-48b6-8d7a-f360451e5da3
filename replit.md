# AI Website Generator

## Overview

This is a conversational AI-powered website generator that uses LangGraph for agentic workflow management. Users can chat with an AI agent to create professional websites through natural language interaction. The system combines Streamlit for the user interface with a sophisticated LangGraph-based agent that can process files, generate websites, and deploy them through an intelligent conversation flow.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

The application follows an agentic architecture with LangGraph-based workflow management:

- **Frontend**: Streamlit web interface for conversational interaction
- **Agentic Core**: LangGraph-based agent with tool integration and state management
- **AI Service**: Google Gemini AI for website generation and conversation
- **File Processing**: Multi-format document parsing (PDF, TXT, DOCX) via tools
- **Deployment**: Automated deployment through agent workflow
- **Memory Management**: Persistent conversation state and user session tracking

## Key Components

### 1. Main Application (`app.py`)
- **Purpose**: Conversational Streamlit interface for AI agent interaction
- **Architecture Decision**: Streamlit chosen for rapid prototyping with agentic integration
- **Key Features**: 
  - Chat-based interface for natural language interaction
  - Real-time conversation state management
  - File upload integration with agent processing
  - Live website preview and deployment workflow
- **Rationale**: Streamlit provides intuitive chat interface while maintaining agentic capabilities

### 2. File Processing (`utils/file_processor.py`)
- **Purpose**: Extract and analyze content from various document formats
- **Supported Formats**: PDF (PyMuPDF), TXT, DOCX (with docx2txt)
- **Architecture Decision**: Multi-format support to maximize user accessibility
- **Features**:
  - Content extraction with format-specific handlers
  - Basic resume analysis (email detection, phone detection, section identification)
  - Content preview generation
- **Rationale**: Flexibility in input formats reduces friction for users

### 3. AI Website Generation (`utils/website_generator.py`)
- **Purpose**: Generate complete HTML websites using Google Gemini AI
- **Architecture Decision**: Google Gemini chosen for its multimodal capabilities and cost-effectiveness
- **Features**:
  - Predefined color schemes (Professional Blue, Modern Dark, Clean Light, Creative Purple)
  - Dynamic prompt engineering based on content type
  - Complete HTML generation with embedded CSS and JavaScript
- **Rationale**: AI-generated websites provide professional quality without requiring design expertise

### 4. Deployment System (`cloudflare_deployment.py`)
- **Purpose**: Real Cloudflare Pages deployment using Wrangler CLI
- **Current State**: Production-ready Cloudflare Pages integration
- **Architecture Decision**: Direct Cloudflare Pages deployment for live website hosting
- **Features**:
  - Real Cloudflare Pages deployment via Wrangler
  - Automatic project name generation with user hints
  - Live URL generation (*.pages.dev domains)
  - Deployment history tracking
  - Environment variable configuration for API credentials
- **Status**: Active production deployment system

### 5. Agentic Core (`agent_core.py`)
- **Purpose**: LangGraph-based conversational AI agent with tool integration
- **Architecture**: State graph with tool-based workflow and memory management
- **Features**:
  - Conversational AI agent with natural language processing
  - Tool integration for file processing, website generation, and deployment
  - State management for conversation flow and user satisfaction tracking
  - Automated user ID generation and project tracking
- **Status**: Core component providing agentic functionality

## Data Flow

1. **User Interaction**: User chats with AI agent through Streamlit interface
2. **Authentication**: Google Gemini API key validation
3. **Agent Processing**: LangGraph agent processes user messages and determines required actions
4. **Tool Execution**: Agent selects and executes appropriate tools (file processing, website generation, deployment)
5. **State Management**: Conversation state and user satisfaction tracking throughout the workflow
6. **Website Generation**: AI generates complete HTML based on processed content and user preferences
7. **Interactive Preview**: Real-time website preview with user feedback integration
8. **Deployment Workflow**: Agent-managed deployment with user confirmation and tracking

## External Dependencies

### Core Dependencies
- **Streamlit**: Web application framework
- **Google Generative AI**: AI-powered website generation
- **PyMuPDF (fitz)**: PDF processing
- **docx2txt**: DOCX file processing (optional)

### Development Dependencies
- **LangGraph**: Advanced AI workflow management (future feature)
- **LangChain**: AI integration framework (future feature)

### API Dependencies
- **Google Gemini API**: Requires valid API key for AI generation

## Deployment Strategy

### Current (Production)
- **Local Development**: Streamlit development server
- **Real Cloudflare Deployment**: Live Cloudflare Pages hosting via Wrangler
- **Live Website Hosting**: Generated websites deployed to *.pages.dev domains
- **API Integration**: Cloudflare API token and account ID authentication

### System Features
- **Wrangler Integration**: Direct deployment using Cloudflare Wrangler CLI
- **Automatic Project Management**: Dynamic project naming with user hints
- **Live URL Generation**: Real https://*.pages.dev domains
- **Deployment History**: JSON-based tracking of all deployments
- **Environment Configuration**: Secure API credential management

### Scalability Considerations
- Modular architecture allows easy replacement of mock services
- Session state management ready for user account integration
- File processing pipeline designed for batch operations
- AI generation system ready for caching and optimization

## Technical Notes

- The application uses session state extensively for maintaining user data across interactions
- Color scheme system is pre-configured but extensible
- File processing includes error handling for unsupported formats
- AI prompts are structured for consistent website generation
- Deployment system includes comprehensive error handling and logging