<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - Full Stack Web Developer</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --background-color: #f8fafc;
            --text-color: #1e293b;
            --light-gray: #e2e8f0;
            --dark-gray: #475569;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--background-color);
            padding: 20px;
        }

        .container {
            max-width: 960px;
            margin: 0 auto;
            background-color: #fff;
            padding: 30px 40px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 1px solid var(--light-gray);
        }

        header h1 {
            color: var(--primary-color);
            font-size: 2.8em;
            margin-bottom: 5px;
            font-weight: 700;
        }

        header p.subtitle {
            font-size: 1.4em;
            color: var(--dark-gray);
            margin-bottom: 20px;
            font-weight: 500;
        }

        .contact-info {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px 30px;
            font-size: 0.95em;
            color: var(--dark-gray);
        }

        .contact-info span, .contact-info a {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .contact-info a {
            color: var(--dark-gray);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .contact-info a:hover {
            color: var(--primary-color);
        }

        section {
            margin-bottom: 35px;
            padding-top: 15px;
        }

        section h2 {
            color: var(--primary-color);
            font-size: 1.8em;
            margin-bottom: 20px;
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 8px;
            display: inline-block;
            font-weight: 600;
        }

        .summary p {
            font-size: 1.1em;
            color: var(--dark-gray);
        }

        .experience-item, .project-item, .education-item {
            margin-bottom: 25px;
            padding-left: 15px;
            border-left: 3px solid var(--light-gray);
        }

        .experience-item:last-child, .project-item:last-child, .education-item:last-child {
            margin-bottom: 0;
        }

        .experience-item h3, .project-item h3 {
            font-size: 1.3em;
            color: var(--text-color);
            margin-bottom: 5px;
            font-weight: 600;
        }

        .experience-item h4 {
            font-size: 1.1em;
            color: var(--primary-color);
            margin-top: 15px;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .experience-item p.role-date, .education-item p.degree-date {
            font-size: 1em;
            color: var(--dark-gray);
            margin-bottom: 10px;
            font-weight: 500;
        }

        .experience-item ul, .project-item ul {
            list-style: none;
            padding-left: 0;
            margin-top: 10px;
        }

        .experience-item ul li, .project-item ul li {
            position: relative;
            padding-left: 25px;
            margin-bottom: 8px;
            color: var(--text-color);
        }

        .experience-item ul li:last-child, .project-item ul li:last-child {
            margin-bottom: 0;
        }

        .experience-item ul li::before, .project-item ul li::before {
            content: '•';
            color: var(--primary-color);
            font-size: 1.2em;
            position: absolute;
            left: 0;
            top: 0;
        }

        .experience-item ul ul { /* For nested project lists */
            margin-top: 5px;
            margin-left: 15px;
        }

        .experience-item ul ul li::before {
            content: '—';
            color: var(--dark-gray);
            font-size: 1em;
        }

        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
        }

        .skill-category h3 {
            font-size: 1.2em;
            color: var(--primary-color);
            margin-bottom: 10px;
            font-weight: 600;
        }

        .skill-category ul {
            list-style: none;
            padding-left: 0;
        }

        .skill-category ul li {
            background-color: var(--light-gray);
            color: var(--text-color);
            padding: 8px 12px;
            margin-bottom: 8px;
            border-radius: 4px;
            display: inline-block;
            margin-right: 8px;
            font-size: 0.95em;
            font-weight: 500;
        }

        .education-item p {
            margin-bottom: 5px;
            color: var(--text-color);
        }

        .education-item p:first-of-type {
            font-weight: 600;
            font-size: 1.1em;
        }

        .certifications ul {
            list-style: none;
            padding-left: 0;
        }

        .certifications ul li {
            position: relative;
            padding-left: 25px;
            margin-bottom: 10px;
            color: var(--text-color);
        }

        .certifications ul li::before {
            content: '★';
            color: var(--primary-color);
            font-size: 1em;
            position: absolute;
            left: 0;
            top: 0;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .container {
                padding: 20px 25px;
            }

            header h1 {
                font-size: 2.2em;
            }

            header p.subtitle {
                font-size: 1.2em;
            }

            .contact-info {
                flex-direction: column;
                align-items: center;
                gap: 10px;
            }

            section h2 {
                font-size: 1.6em;
                margin-bottom: 15px;
            }

            .experience-item h3, .project-item h3 {
                font-size: 1.2em;
            }

            .experience-item p.role-date, .education-item p.degree-date {
                font-size: 0.95em;
            }

            .experience-item ul li, .project-item ul li {
                font-size: 0.95em;
            }

            .skills-grid {
                grid-template-columns: 1fr; /* Stack skill categories on small screens */
            }

            .skill-category ul li {
                font-size: 0.9em;
                padding: 6px 10px;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 10px;
            }

            .container {
                padding: 15px 20px;
            }

            header h1 {
                font-size: 1.8em;
            }

            header p.subtitle {
                font-size: 1em;
            }

            .contact-info span, .contact-info a {
                font-size: 0.85em;
            }

            section h2 {
                font-size: 1.4em;
            }

            .summary p {
                font-size: 1em;
            }

            .experience-item, .project-item, .education-item {
                padding-left: 10px;
            }

            .experience-item h3, .project-item h3 {
                font-size: 1.1em;
            }

            .skill-category h3 {
                font-size: 1.1em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Manzoor Ah Chopan</h1>
            <p class="subtitle">Full Stack Web Developer</p>
            <div class="contact-info">
                <span>📞 Phone: <a href="tel:7780883346">7780883346</a></span>
                <span>✉ Email: <a href="mailto:<EMAIL>"><EMAIL></a></span>
                <span>📍 Address: Srinagar, Jammu & Kashmir</span>
                <span>🔗 LinkedIn: <a href="https://linkedin.com/in/manzoor-chopan-074b14201" target="_blank" rel="noopener noreferrer">linkedin.com/in/manzoor-chopan-074b14201</a></span>
                <span>🔗 GitHub: <a href="https://github.com/mchopan" target="_blank" rel="noopener noreferrer">github.com/mchopan</a></span>
                <span>🔗 Portfolio: <a href="https://mchopan.github.io/portfolio" target="_blank" rel="noopener noreferrer">mchopan.github.io/portfolio</a></span>
            </div>
        </header>

        <main>
            <section class="summary">
                <h2>Professional Summary</h2>
                <p>Full Stack Web Developer with experience in designing and developing web applications using modern technologies. Proficient in front-end and back-end development, with a focus on creating user-friendly interfaces and scalable solutions. Adept at collaborating with cross-functional teams to deliver impactful digital experiences.</p>
            </section>

            <section class="experience">
                <h2>Professional Experience</h2>
                <div class="experience-item">
                    <h3>Cogveel Technologies | Full Stack Developer</h3>
                    <p class="role-date">Sep 2023 – Present</p>
                    <ul>
                        <li>Developed and maintained web applications using React, Node.js, and MongoDB.</li>
                        <li>Built cross-platform mobile applications using React Native.</li>
                        <li>Collaborated with teams to design and implement new features.</li>
                        <li>Enhanced application performance and user experience.</li>
                    </ul>
                    <h4>Key Projects:</h4>
                    <ul>
                        <li>Neuquip: Solely developed the frontend of this AI-powered application with features including:
                            <ul>
                                <li>Uploading files (PDF, Excel, TXT, Word) and enabling conversational interaction with document content.</li>
                                <li>Generating analytics and exporting them into a sketchbook for document creation.</li>
                                <li>Creating workflows for sketchbooks, with sharing functionality among multiple users.</li>
                                <li>Designing an enhanced user interface to support complex workflows.</li>
                            </ul>
                        </li>
                        <li>Apple Doc: Redesigned the UI and added new features to this orchid plant treatment app.</li>
                        <li>Wabi Sabi: Contributed to the development of an e-commerce platform as part of a team.</li>
                        <li>Quick Load: Created a booking app for trucks from scratch, handling both backend and frontend development.</li>
                    </ul>
                </div>

                <div class="experience-item">
                    <h3>Red Stag Labs | Full Stack Developer</h3>
                    <p class="role-date">Nov 2022 – May 2023</p>
                    <ul>
                        <li>Designed and developed a dynamic full-stack web application using ReactJS, Material-UI, CSS, Spring Boot, and MySQL.</li>
                        <li>Focused on creating user-friendly interfaces and enhancing interactivity.</li>
                        <li>Contributed to the project marvelminds.in.</li>
                    </ul>
                </div>

                <div class="experience-item">
                    <h3>Oxford Skill and Education Institute | Web Developer</h3>
                    <p class="role-date">Feb 2022 – Mar 2022</p>
                    <ul>
                        <li>Developed a comprehensive registration platform for students.</li>
                        <li>Improved the institute's online presence and streamlined the registration process.</li>
                    </ul>
                </div>
            </section>

            <section class="projects">
                <h2>Personal Projects</h2>
                <div class="project-item">
                    <h3>site-bot (NPM Package)</h3>
                    <p>Developed a highly customizable chatbot as an npm package designed to work seamlessly with any JavaScript framework or library.</p>
                    <ul>
                        <li>AI Integration: Utilizes an AI API key provided by the library user, enabling dynamic and context-aware interactions.</li>
                        <li>Customizable and Flexible: Designed to be easily integrated into any website, providing developers with the flexibility to customize the chatbot's behavior and appearance.</li>
                        <li>Cross-Platform Compatibility: Built with compatibility across modern JavaScript frameworks, making it adaptable for diverse project requirements.</li>
                        <li>Developer-Friendly: Created with a focus on simplicity and ease of use, allowing developers to set up the chatbot with minimal configuration.</li>
                        <li>Published on NPM: The package is published on npm, making it easily accessible for public use. (search in npm : mchopan/sitebot)</li>
                    </ul>
                </div>

                <div class="project-item">
                    <h3>Campus Command (React Native | Final Year Project)</h3>
                    <ul>
                        <li>Designed and developed a messaging app tailored for educational institutions.</li>
                        <li>Enabled colleges to create semesters/groups and manage student registrations by registration numbers.</li>
                        <li>Built a student dashboard that displays only relevant semester/group data.</li>
                        <li>Integrated features like group chat, updates, and collaboration tools.</li>
                        <li>Solely responsible for the app's design, development, and deployment.</li>
                    </ul>
                </div>

                <div class="project-item">
                    <h3>Portfolio Website</h3>
                    <p>Showcased professional skills and projects.</p>
                </div>
                <div class="project-item">
                    <h3>CASET College Website</h3>
                    <p>Built a dynamic website for the college.</p>
                </div>
                <div class="project-item">
                    <h3>Mehndi Artist Portfolio</h3>
                    <p>Designed an elegant portfolio for a mehndi artist.</p>
                </div>
                <div class="project-item">
                    <h3>News Website</h3>
                    <p>Developed a news website leveraging the News API.</p>
                </div>
                <div class="project-item">
                    <h3>Facebook Clone</h3>
                    <p>Built a front-end clone of Facebook.</p>
                </div>
                <div class="project-item">
                    <h3>Itinerary Website</h3>
                    <p>Created a website for booking travel-related services.</p>
                </div>
            </section>

            <section class="education">
                <h2>Education</h2>
                <div class="education-item">
                    <p><strong>Caset College of Computer Science</strong></p>
                    <p class="degree-date">Bachelor of Computer Applications (BCA) | 2021 – 2023</p>
                </div>
                <div class="education-item">
                    <p><strong>Govt Boys Higher Secondary (JKBOSE)</strong></p>
                    <p class="degree-date">2019 – 2020</p>
                </div>
                <div class="education-item">
                    <p><strong>Maulana Azad Education Foundation</strong></p>
                    <p class="degree-date">Web Designing & Publishing Assistant | 2017 – 2018</p>
                </div>
                <div class="education-item">
                    <p><strong>Govt Boys High School (JKBOSE)</strong></p>
                    <p class="degree-date">2016 – 2017</p>
                </div>
            </section>

            <section class="skills">
                <h2>Skills</h2>
                <div class="skills-grid">
                    <div class="skill-category">
                        <h3>Front-End</h3>
                        <ul>
                            <li>React</li>
                            <li>HTML</li>
                            <li>CSS</li>
                            <li>JavaScript</li>
                            <li>Material-UI</li>
                        </ul>
                    </div>
                    <div class="skill-category">
                        <h3>Back-End</h3>
                        <ul>
                            <li>Node.js</li>
                            <li>Spring Boot</li>
                            <li>MySQL</li>
                            <li>MongoDB</li>
                        </ul>
                    </div>
                    <div class="skill-category">
                        <h3>Mobile Development</h3>
                        <ul>
                            <li>React Native</li>
                        </ul>
                    </div>
                    <div class="skill-category">
                        <h3>Other</h3>
                        <ul>
                            <li>Git</li>
                            <li>Web Designing & Publishing</li>
                        </ul>
                    </div>
                </div>
            </section>

            <section class="certifications">
                <h2>Certifications</h2>
                <ul>
                    <li>Full Stack Web Development – Red Stag Labs, Qamarwari, Srinagar</li>
                    <li>Linux Fundamentals – Kimo.ai</li>
                    <li>Hands-On Linux System Administration – Kimo.ai</li>
                    <li>Data Structures and Algorithms – Kimo.ai</li>
                </ul>
            </section>
        </main>
    </div>
</body>
</html>