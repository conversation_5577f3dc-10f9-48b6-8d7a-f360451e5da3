import os
import tempfile
import time
from utils.file_processor import process_resume_file, process_instruction_file
from utils.website_generator import generate_website_with_ai
from utils.deployment import mock_deploy_website
from cloudflare_deployment import deploy_to_cloudflare
from cloudflare_api_deployment import deploy_with_api
from simple_file_deployment import deploy_locally

class SimpleWebsiteAgent:
    """Simple website generator agent that avoids rate limits"""
    
    def __init__(self):
        self.conversation_history = []
        self.current_content = ""
        self.current_html = ""
        self.website_created = False
        self.awaiting_confirmation = False
        self.last_request_time = 0
        self.min_request_interval = 15  # 15 seconds between requests to avoid rate limits
    
    def _rate_limit_check(self):
        """Simple rate limiting"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_request_interval:
            wait_time = self.min_request_interval - time_since_last
            return wait_time
        return 0
    
    def process_file(self, file_path: str, file_type: str = "resume") -> str:
        """Process uploaded file"""
        try:
            if file_type == "resume":
                content = process_resume_file(file_path)
            else:
                content = process_instruction_file(file_path)
            
            self.current_content = content
            self.conversation_history.append(("user", f"Uploaded {file_type} file"))
            self.conversation_history.append(("assistant", "File processed successfully! I can now create a website based on this content."))
            
            return "File processed successfully! I can now create a website based on this content."
        except Exception as e:
            return f"Error processing file: {str(e)}"
    
    def generate_website(self, website_type: str = "Portfolio/Resume", color_scheme: str = "Professional Blue") -> str:
        """Generate website with rate limiting"""
        if not self.current_content:
            return "Please upload a file or provide content first."
        
        # Check rate limit
        wait_time = self._rate_limit_check()
        if wait_time > 0:
            return f"Please wait {int(wait_time)} seconds before generating to avoid rate limits."
        
        try:
            self.last_request_time = time.time()
            
            # Generate website
            html_content = generate_website_with_ai(
                content=self.current_content,
                website_type=website_type,
                color_scheme=color_scheme
            )
            
            self.current_html = html_content
            self.website_created = True
            self.awaiting_confirmation = True
            
            self.conversation_history.append(("user", f"Generate {website_type} website with {color_scheme} theme"))
            self.conversation_history.append(("assistant", "Website generated successfully! Check the preview and let me know if you're satisfied."))
            
            return "Website generated successfully! Check the preview and let me know if you're satisfied with it."
            
        except Exception as e:
            return f"Error generating website: {str(e)}"
    
    def deploy_website(self) -> str:
        """Deploy the current website to Cloudflare Pages"""
        if not self.current_html:
            return "No website to deploy. Please generate a website first."
        
        if not self.awaiting_confirmation:
            return "Please confirm you're satisfied with the website first."
        
        try:
            # For now, use local deployment due to interactive authentication requirements
            deployment_result = deploy_locally(self.current_html)
            
            self.conversation_history.append(("user", "Deploy website"))
            self.conversation_history.append(("assistant", deployment_result))
            
            return deployment_result
            
        except Exception as e:
            return f"Deployment error: {str(e)}"
    
    def process_message(self, message: str) -> str:
        """Process user message and return response"""
        message_lower = message.lower()
        
        # Handle satisfaction responses
        if self.awaiting_confirmation:
            if any(word in message_lower for word in ["yes", "satisfied", "good", "perfect", "publish", "deploy"]):
                return self.deploy_website()
            elif any(word in message_lower for word in ["no", "change", "modify", "different"]):
                self.awaiting_confirmation = False
                return "What changes would you like me to make to the website?"
        
        # Handle generation requests
        if any(word in message_lower for word in ["generate", "create", "build", "make"]):
            if "dark" in message_lower:
                return self.generate_website("Portfolio/Resume", "Modern Dark")
            elif "purple" in message_lower:
                return self.generate_website("Portfolio/Resume", "Creative Purple")
            elif "green" in message_lower:
                return self.generate_website("Portfolio/Resume", "Nature Green")
            elif "light" in message_lower:
                return self.generate_website("Portfolio/Resume", "Clean Light")
            else:
                return self.generate_website("Portfolio/Resume", "Professional Blue")
        
        # Handle deployment requests
        if any(word in message_lower for word in ["deploy", "publish", "go live"]):
            if self.website_created:
                return self.deploy_website()
            else:
                return "Please generate a website first before deploying."
        
        # Default responses
        if not self.current_content:
            return "Hi! I'm your website developer. Please upload a resume/CV file or describe what kind of website you want, and I'll create it for you."
        elif not self.website_created:
            return "I have your content ready. Would you like me to generate a website? You can ask for specific themes like 'Create a dark theme website' or 'Generate a professional blue website'."
        elif self.awaiting_confirmation:
            return "Are you satisfied with the website? Say 'yes' to publish it or tell me what changes you'd like."
        else:
            return "How can I help you with your website?"
    
    def get_state(self) -> dict:
        """Get current agent state"""
        return {
            "has_content": bool(self.current_content),
            "website_created": self.website_created,
            "awaiting_confirmation": self.awaiting_confirmation,
            "html_content": self.current_html,
            "conversation_history": self.conversation_history
        }
    
    def reset(self):
        """Reset the agent state"""
        self.conversation_history = []
        self.current_content = ""
        self.current_html = ""
        self.website_created = False
        self.awaiting_confirmation = False

# Singleton instance
_simple_agent = None

def get_simple_agent():
    """Get singleton simple agent instance"""
    global _simple_agent
    if _simple_agent is None:
        _simple_agent = SimpleWebsiteAgent()
    return _simple_agent