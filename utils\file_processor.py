import fitz  # PyMuPDF
import os
from typing import Dict, Any

def process_resume_file(file_path: str) -> str:
    """
    Process uploaded resume/CV file to extract information for portfolio website.
    Supports PDF and text files.
    
    Args:
        file_path: Path to the uploaded resume file
    
    Returns:
        Extracted information formatted for website creation
    """
    try:
        if file_path.lower().endswith('.pdf'):
            # Extract text from PDF
            doc = fitz.open(file_path)
            text = ""
            for page in doc:
                text += page.get_text()
            doc.close()
        elif file_path.lower().endswith('.txt'):
            # Handle text files
            with open(file_path, 'r', encoding='utf-8') as f:
                text = f.read()
        elif file_path.lower().endswith('.docx'):
            # For DOCX files, we'll extract as much as possible
            # Note: This is a simplified approach
            try:
                import docx2txt
                text = docx2txt.process(file_path)
            except ImportError:
                # Fallback if docx2txt is not available
                raise Exception("DOCX processing requires docx2txt library. Please install it or use PDF/TXT format.")
        else:
            raise Exception(f"Unsupported file format: {file_path.split('.')[-1]}")
        
        # Basic information extraction
        info = analyze_resume_content(text)
        
        result = f"📄 Resume/CV processed successfully!\n\n"
        result += f"📊 Content Analysis:\n"
        result += f"   - Length: {info['length']} characters\n"
        result += f"   - Contains email: {'✅' if info['has_email'] else '❌'}\n"
        result += f"   - Contains phone: {'✅' if info['has_phone'] else '❌'}\n"
        result += f"   - Detected sections: {', '.join(info['sections']) if info['sections'] else 'None detected'}\n\n"
        result += f"📝 Content Preview:\n{text[:1000]}{'...' if len(text) > 1000 else ''}\n\n"
        result += f"Raw content for AI processing:\n{text}"
        
        return result
        
    except Exception as e:
        raise Exception(f"Error processing resume file: {str(e)}")

def process_instruction_file(file_path: str) -> str:
    """
    Process uploaded instruction file for specific website requirements.
    
    Args:
        file_path: Path to the uploaded instruction file
    
    Returns:
        Processed instructions for website creation
    """
    try:
        if file_path.lower().endswith('.pdf'):
            # Extract text from PDF
            doc = fitz.open(file_path)
            text = ""
            for page in doc:
                text += page.get_text()
            doc.close()
        elif file_path.lower().endswith('.txt'):
            # Handle text files
            with open(file_path, 'r', encoding='utf-8') as f:
                text = f.read()
        elif file_path.lower().endswith('.docx'):
            # For DOCX files
            try:
                import docx2txt
                text = docx2txt.process(file_path)
            except ImportError:
                raise Exception("DOCX processing requires docx2txt library. Please install it or use PDF/TXT format.")
        else:
            raise Exception(f"Unsupported file format: {file_path.split('.')[-1]}")
        
        result = f"📋 Instructions processed successfully!\n\n"
        result += f"📝 Your requirements:\n{text}\n\n"
        result += f"Content for AI processing:\n{text}"
        
        return result
        
    except Exception as e:
        raise Exception(f"Error processing instruction file: {str(e)}")

def analyze_resume_content(text: str) -> Dict[str, Any]:
    """
    Analyze resume content to extract basic information.
    
    Args:
        text: Resume text content
    
    Returns:
        Dictionary with analyzed information
    """
    info = {
        "raw_text": text,
        "length": len(text),
        "has_email": "@" in text,
        "has_phone": any(char.isdigit() for char in text),
        "sections": []
    }
    
    # Try to identify common resume sections
    common_sections = [
        "experience", "education", "skills", "projects", 
        "summary", "objective", "certifications", "achievements",
        "work experience", "employment", "qualifications",
        "accomplishments", "awards", "publications"
    ]
    
    text_lower = text.lower()
    for section in common_sections:
        if section in text_lower:
            info["sections"].append(section)
    
    # Remove duplicates and sort
    info["sections"] = sorted(list(set(info["sections"])))
    
    return info
