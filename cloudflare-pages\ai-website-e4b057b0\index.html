<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - Full Stack Web Developer</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        /* Global Styles & Reset */
        *, *::before, *::after {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8fafc; /* Background color */
            color: #1e293b; /* Text color */
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 960px;
            margin: 40px auto;
            padding: 0 20px;
            background-color: #ffffff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }

        /* Header Section */
        .header {
            text-align: center;
            padding: 40px 0 30px;
            border-bottom: 1px solid #e2e8f0;
        }

        .header h1 {
            font-size: 2.8em;
            color: #2563eb; /* Primary color */
            margin-bottom: 5px;
            font-weight: 700;
        }

        .header p.title {
            font-size: 1.4em;
            color: #475569;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .contact-info {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px 30px;
            font-size: 0.95em;
            color: #475569;
        }

        .contact-info span, .contact-info a {
            display: flex;
            align-items: center;
            gap: 8px;
            white-space: nowrap;
        }

        .contact-info a {
            color: #2563eb;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .contact-info a:hover {
            color: #1d4ed8;
            text-decoration: underline;
        }

        /* Icons (SVG data URIs for embedded icons) */
        .icon {
            display: inline-block;
            width: 1em;
            height: 1em;
            background-repeat: no-repeat;
            background-size: contain;
            vertical-align: middle;
            filter: invert(30%) sepia(90%) saturate(1000%) hue-rotate(210deg) brightness(80%) contrast(90%); /* To color icons */
        }

        .icon-phone { background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M6.62 10.79a15.054 15.054 0 006.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24a11.232 11.232 0 014.9 1.48c.34.13.56.46.56.85v3.49c0 .54-.45.99-.99.99H5c-.55 0-1-.45-1-1C4 12.01 12.01 4 20 4c.55 0 1 .45 1 1v3.49c0 .39-.22.72-.56.85a11.232 11.232 0 01-1.48 4.9c-.12.35-.03.75.24 1.02l2.2 2.2z"/></svg>'); }
        .icon-email { background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/></svg>'); }
        .icon-location { background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5S10.62 6.5 12 6.5s2.5 1.12 2.5 2.5S13.38 11.5 12 11.5z"/></svg>'); }
        .icon-linkedin { background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/></svg>'); }
        .icon-github { background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.08-.731.084-.716.084-.716 1.205.085 1.838 1.238 1.838 1.238 1.07 1.835 2.809 1.305 3.492.998.108-.77.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.046.138 3.003.404 2.294-1.552 3.302-1.23 3.302-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576C20.56 21.802 24 17.302 24 12c0-6.627-5.373-12-12-12z"/></svg>'); }
        .icon-portfolio { background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M20 6h-4V4c0-1.11-.89-2-2-2h-4c-1.11 0-2 .89-2 2v2H4c-1.11 0-1.99.89-1.99 2L2 19c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2zm-6 0h-4V4h4v2z"/></svg>'); }


        /* Main Content Sections */
        .section {
            padding: 30px 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .section:last-child {
            border-bottom: none;
            padding-bottom: 40px; /* More padding for the last section */
        }

        .section h2 {
            color: #2563eb;
            font-size: 1.8em;
            margin-bottom: 20px;
            border-bottom: 2px solid #2563eb;
            display: inline-block;
            padding-bottom: 5px;
            font-weight: 600;
        }

        /* Summary */
        .summary p {
            font-size: 1.1em;
            color: #334155;
        }

        /* Experience and Projects */
        .experience-item, .project-item, .education-item {
            margin-bottom: 25px;
        }

        .experience-item h3, .project-item h3 {
            font-size: 1.4em;
            color: #1e293b;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .experience-item .role, .experience-item .date {
            font-size: 1em;
            color: #475569;
            margin-bottom: 10px;
            display: block;
        }

        .experience-item .role {
            font-weight: 600;
        }

        .experience-item .date {
            font-style: italic;
        }

        .experience-item ul, .project-item ul {
            list-style: none; /* Remove default bullet */
            padding-left: 0;
            margin-top: 10px;
        }

        .experience-item ul li, .project-item ul li {
            position: relative;
            padding-left: 25px;
            margin-bottom: 8px;
            font-size: 0.95em;
            color: #334155;
        }

        .experience-item ul li::before, .project-item ul li::before {
            content: '•'; /* Custom bullet point */
            color: #2563eb;
            font-weight: bold;
            display: inline-block;
            width: 1em;
            margin-left: -1em;
            position: absolute;
            left: 0;
            top: 0;
        }

        /* Nested lists for projects under experience */
        .experience-item .key-projects {
            margin-top: 15px;
            padding-left: 0;
        }
        .experience-item .key-projects h4 {
            font-size: 1.1em;
            color: #1e293b;
            margin-bottom: 10px;
            font-weight: 600;
        }
        .experience-item .key-projects ul {
            list-style: none;
            padding-left: 0;
        }
        .experience-item .key-projects ul li {
            padding-left: 25px;
            margin-bottom: 5px;
            font-size: 0.9em;
            color: #334155;
        }
        .experience-item .key-projects ul li::before {
            content: '–'; /* Different bullet for nested items */
            color: #475569;
        }
        .experience-item .key-projects ul ul li::before { /* For deepest nesting like Neuquip points */
            content: '•';
            color: #94a3b8;
        }

        /* Education */
        .education-list {
            list-style: none;
            padding-left: 0;
        }

        .education-item {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
            font-size: 1em;
            color: #334155;
            flex-wrap: wrap; /* Allow wrapping on smaller screens */
        }

        .education-item .degree, .education-item .institution {
            font-weight: 600;
            color: #1e293b;
        }

        .education-item .date {
            font-style: italic;
            color: #475569;
            flex-shrink: 0; /* Prevent date from shrinking */
            margin-left: 10px; /* Space between text and date */
        }
        @media (max-width: 600px) {
            .education-item {
                flex-direction: column;
                align-items: flex-start;
            }
            .education-item .date {
                margin-top: 5px;
                margin-left: 0;
            }
        }


        /* Skills */
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .skill-category h3 {
            font-size: 1.2em;
            color: #2563eb;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .skill-category ul {
            list-style: none;
            padding-left: 0;
        }

        .skill-category ul li {
            margin-bottom: 5px;
            font-size: 0.95em;
            color: #334155;
        }
        .skill-category ul li::before {
            content: '•';
            color: #2563eb;
            font-weight: bold;
            display: inline-block;
            width: 1em;
            margin-left: -1em;
        }


        /* Certifications */
        .certifications-list {
            list-style: none;
            padding-left: 0;
        }

        .certifications-list li {
            margin-bottom: 8px;
            font-size: 0.95em;
            color: #334155;
            position: relative;
            padding-left: 20px;
        }

        .certifications-list li::before {
            content: '•';
            color: #2563eb;
            font-weight: bold;
            display: inline-block;
            width: 1em;
            margin-left: -1em;
            position: absolute;
            left: 0;
            top: 0;
        }

        /* Responsive Adjustments */
        @media (max-width: 768px) {
            .container {
                margin: 20px auto;
                padding: 0 15px;
            }

            .header h1 {
                font-size: 2.2em;
            }

            .header p.title {
                font-size: 1.2em;
            }

            .contact-info {
                flex-direction: column;
                align-items: center;
                gap: 10px;
            }

            .section h2 {
                font-size: 1.6em;
                margin-bottom: 15px;
            }

            .experience-item h3, .project-item h3 {
                font-size: 1.2em;
            }

            .experience-item .role, .experience-item .date {
                font-size: 0.9em;
            }

            .experience-item ul li, .project-item ul li {
                font-size: 0.9em;
            }

            .skills-grid {
                grid-template-columns: 1fr; /* Stack skill categories on small screens */
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.8em;
            }
            .header p.title {
                font-size: 1em;
            }
            .contact-info {
                font-size: 0.85em;
            }
            .section h2 {
                font-size: 1.4em;
            }
            .summary p {
                font-size: 1em;
            }
        }

    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>Manzoor Ah Chopan</h1>
            <p class="title">Full Stack Web Developer</p>
            <div class="contact-info">
                <span><span class="icon icon-phone"></span> <a href="tel:7780883346">7780883346</a></span>
                <span><span class="icon icon-email"></span> <a href="mailto:<EMAIL>"><EMAIL></a></span>
                <span><span class="icon icon-location"></span> Srinagar, Jammu & Kashmir</span>
                <span><span class="icon icon-linkedin"></span> <a href="https://linkedin.com/in/manzoor-chopan-074b14201" target="_blank" rel="noopener noreferrer">linkedin.com/in/manzoor-chopan-074b14201</a></span>
                <span><span class="icon icon-github"></span> <a href="https://github.com/mchopan" target="_blank" rel="noopener noreferrer">github.com/mchopan</a></span>
                <span><span class="icon icon-portfolio"></span> <a href="https://mchopan.github.io/portfolio" target="_blank" rel="noopener noreferrer">mchopan.github.io/portfolio</a></span>
            </div>
        </header>

        <main>
            <section class="section summary">
                <h2>Professional Summary</h2>
                <p>Full Stack Web Developer with experience in designing and developing web applications using modern technologies. Proficient in front-end and back-end development, with a focus on creating user-friendly interfaces and scalable solutions. Adept at collaborating with cross-functional teams to deliver impactful digital experiences.</p>
            </section>

            <section class="section experience">
                <h2>Professional Experience</h2>
                <div class="experience-item">
                    <h3>Cogveel Technologies</h3>
                    <p class="role">Full Stack Developer</p>
                    <p class="date">Sep 2023 – Present</p>
                    <ul>
                        <li>Developed and maintained web applications using React, Node.js, and MongoDB.</li>
                        <li>Built cross-platform mobile applications using React Native.</li>
                        <li>Collaborated with teams to design and implement new features.</li>
                        <li>Enhanced application performance and user experience.</li>
                    </ul>
                    <div class="key-projects">
                        <h4>Key Projects:</h4>
                        <ul>
                            <li>Neuquip: Solely developed the frontend of this AI-powered application with features including:
                                <ul>
                                    <li>Uploading files (PDF, Excel, TXT, Word) and enabling conversational interaction with document content.</li>
                                    <li>Generating analytics and exporting them into a sketchbook for document creation.</li>
                                    <li>Creating workflows for sketchbooks, with sharing functionality among multiple users.</li>
                                    <li>Designing an enhanced user interface to support complex workflows.</li>
                                </ul>
                            </li>
                            <li>Apple Doc: Redesigned the UI and added new features to this orchid plant treatment app.</li>
                            <li>Wabi Sabi: Contributed to the development of an e-commerce platform as part of a team.</li>
                            <li>Quick Load: Created a booking app for trucks from scratch, handling both backend and frontend development.</li>
                        </ul>
                    </div>
                </div>

                <div class="experience-item">
                    <h3>Red Stag Labs</h3>
                    <p class="role">Full Stack Developer</p>
                    <p class="date">Nov 2022 – May 2023</p>
                    <ul>
                        <li>Designed and developed a dynamic full-stack web application using ReactJS, Material-UI, CSS, Spring Boot, and MySQL.</li>
                        <li>Focused on creating user-friendly interfaces and enhancing interactivity.</li>
                        <li>Contributed to the project marvelminds.in.</li>
                    </ul>
                </div>

                <div class="experience-item">
                    <h3>Oxford Skill and Education Institute</h3>
                    <p class="role">Web Developer</p>
                    <p class="date">Feb 2022 – Mar 2022</p>
                    <ul>
                        <li>Developed a comprehensive registration platform for students.</li>
                        <li>Improved the institute's online presence and streamlined the registration process.</li>
                    </ul>
                </div>
            </section>

            <section class="section projects">
                <h2>Personal Projects</h2>
                <div class="project-item">
                    <h3>site-bot (NPM Package)</h3>
                    <ul>
                        <li>Developed a highly customizable chatbot as an npm package designed to work seamlessly with any JavaScript framework or library.</li>
                        <li>AI Integration: Utilizes an AI API key provided by the library user, enabling dynamic and context-aware interactions.</li>
                        <li>Customizable and Flexible: Designed to be easily integrated into any website, providing developers with the flexibility to customize the chatbot's behavior and appearance.</li>
                        <li>Cross-Platform Compatibility: Built with compatibility across modern JavaScript frameworks, making it adaptable for diverse project requirements.</li>
                        <li>Developer-Friendly: Created with a focus on simplicity and ease of use, allowing developers to set up the chatbot with minimal configuration.</li>
                        <li>Published on NPM: The package is published on npm, making it easily accessible for public use. (search in npm : mchopan/sitebot)</li>
                    </ul>
                </div>
                <div class="project-item">
                    <h3>Campus Command (React Native | Final Year Project)</h3>
                    <ul>
                        <li>Designed and developed a messaging app tailored for educational institutions.</li>
                        <li>Enabled colleges to create semesters/groups and manage student registrations by registration numbers.</li>
                        <li>Built a student dashboard that displays only relevant semester/group data.</li>
                        <li>Integrated features like group chat, updates, and collaboration tools.</li>
                        <li>Solely responsible for the app's design, development, and deployment.</li>
                    </ul>
                </div>
                <div class="project-item">
                    <h3>Portfolio Website</h3>
                    <ul>
                        <li>Showcased professional skills and projects.</li>
                    </ul>
                </div>
                <div class="project-item">
                    <h3>CASET College Website</h3>
                    <ul>
                        <li>Built a dynamic website for the college.</li>
                    </ul>
                </div>
                <div class="project-item">
                    <h3>Mehndi Artist Portfolio</h3>
                    <ul>
                        <li>Designed an elegant portfolio for a mehndi artist.</li>
                    </ul>
                </div>
                <div class="project-item">
                    <h3>News Website</h3>
                    <ul>
                        <li>Developed a news website leveraging the News API.</li>
                    </ul>
                </div>
                <div class="project-item">
                    <h3>Facebook Clone</h3>
                    <ul>
                        <li>Built a front-end clone of Facebook.</li>
                    </ul>
                </div>
                <div class="project-item">
                    <h3>Itinerary Website</h3>
                    <ul>
                        <li>Created a website for booking travel-related services.</li>
                    </ul>
                </div>
            </section>

            <section class="section education">
                <h2>Education</h2>
                <ul class="education-list">
                    <li class="education-item">
                        <div>
                            <span class="degree">Bachelor of Computer Applications (BCA)</span><br>
                            <span class="institution">Caset College of Computer Science</span>
                        </div>
                        <span class="date">2021 – 2023</span>
                    </li>
                    <li class="education-item">
                        <div>
                            <span class="institution">Govt Boys Higher Secondary (JKBOSE)</span>
                        </div>
                        <span class="date">2019 – 2020</span>
                    </li>
                    <li class="education-item">
                        <div>
                            <span class="degree">Web Designing & Publishing Assistant</span><br>
                            <span class="institution">Maulana Azad Education Foundation</span>
                        </div>
                        <span class="date">2017 – 2018</span>
                    </li>
                    <li class="education-item">
                        <div>
                            <span class="institution">Govt Boys High School (JKBOSE)</span>
                        </div>
                        <span class="date">2016 – 2017</span>
                    </li>
                </ul>
            </section>

            <section class="section skills">
                <h2>Skills</h2>
                <div class="skills-grid">
                    <div class="skill-category">
                        <h3>Front-End</h3>
                        <ul>
                            <li>React</li>
                            <li>HTML</li>
                            <li>CSS</li>
                            <li>JavaScript</li>
                            <li>Material-UI</li>
                        </ul>
                    </div>
                    <div class="skill-category">
                        <h3>Back-End</h3>
                        <ul>
                            <li>Node.js</li>
                            <li>Spring Boot</li>
                            <li>MySQL</li>
                            <li>MongoDB</li>
                        </ul>
                    </div>
                    <div class="skill-category">
                        <h3>Mobile Development</h3>
                        <ul>
                            <li>React Native</li>
                        </ul>
                    </div>
                    <div class="skill-category">
                        <h3>Other</h3>
                        <ul>
                            <li>Git</li>
                            <li>Web Designing & Publishing</li>
                        </ul>
                    </div>
                </div>
            </section>

            <section class="section certifications">
                <h2>Certifications</h2>
                <ul class="certifications-list">
                    <li>Full Stack Web Development – Red Stag Labs, Qamarwari, Srinagar</li>
                    <li>Linux Fundamentals – Kimo.ai</li>
                    <li>Hands-On Linux System Administration – Kimo.ai</li>
                    <li>Data Structures and Algorithms – Kimo.ai</li>
                </ul>
            </section>
        </main>
    </div>
</body>
</html>