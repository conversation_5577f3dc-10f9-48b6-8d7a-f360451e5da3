import os
import json
from google import genai
from google.genai import types

def generate_website_with_ai(content: str, website_type: str = "Portfolio/Resume", color_scheme: str = "Professional Blue") -> str:
    """
    Generate a complete HTML website using Google Gemini AI.
    
    Args:
        content: Processed content from resume or instructions
        website_type: Type of website to generate
        color_scheme: Color scheme for the website
    
    Returns:
        Complete HTML content
    """
    api_key = os.environ.get("GEMINI_API_KEY")
    if not api_key:
        raise Exception("GEMINI_API_KEY environment variable is not set")
    
    client = genai.Client(api_key=api_key)
    
    # Define color schemes
    color_schemes = {
        "Professional Blue": {
            "primary": "#2563eb",
            "secondary": "#1e40af", 
            "accent": "#3b82f6",
            "background": "#f8fafc",
            "text": "#1e293b"
        },
        "Modern Dark": {
            "primary": "#6366f1",
            "secondary": "#4f46e5",
            "accent": "#8b5cf6", 
            "background": "#0f172a",
            "text": "#f1f5f9"
        },
        "Clean Light": {
            "primary": "#059669",
            "secondary": "#047857",
            "accent": "#10b981",
            "background": "#ffffff", 
            "text": "#374151"
        },
        "Creative Purple": {
            "primary": "#7c3aed",
            "secondary": "#6d28d9",
            "accent": "#a855f7",
            "background": "#faf5ff",
            "text": "#581c87"
        },
        "Nature Green": {
            "primary": "#16a34a",
            "secondary": "#15803d", 
            "accent": "#22c55e",
            "background": "#f0fdf4",
            "text": "#14532d"
        }
    }
    
    selected_colors = color_schemes.get(color_scheme, color_schemes["Professional Blue"])
    
    prompt = f"""Create a complete HTML5 website using this content:

{content}

Requirements:
- Website type: {website_type}
- Colors: Primary {selected_colors['primary']}, Background {selected_colors['background']}, Text {selected_colors['text']}
- Complete HTML5 document with embedded CSS
- Responsive design with modern layout
- Professional styling appropriate for {website_type}

Generate ONLY clean HTML code with embedded CSS. No explanations or markdown."""
    
    try:
        response = client.models.generate_content(
            model="gemini-2.5-flash",  # Use faster model for better performance
            contents=prompt,
            config=types.GenerateContentConfig(
                temperature=0.7,
                max_output_tokens=32000,  # Increase token limit significantly
            )
        )
        
        # Debug information
        print(f"Gemini API Response Status: {response}")
        
        if response and response.candidates and len(response.candidates) > 0:
            candidate = response.candidates[0]
            if hasattr(candidate, 'content') and candidate.content and candidate.content.parts:
                # Extract text from the first part
                html_content = candidate.content.parts[0].text if candidate.content.parts[0].text else ""
                html_content = html_content.strip()
                print(f"Generated HTML length: {len(html_content)} characters")
            else:
                html_content = ""
        elif response and hasattr(response, 'text') and response.text:
            html_content = response.text.strip()
            print(f"Generated HTML length: {len(html_content)} characters")
        
        if html_content:
            # Clean up the response - remove any markdown formatting if present
            if html_content.startswith('```html'):
                html_content = html_content[7:]
            if html_content.endswith('```'):
                html_content = html_content[:-3]
            
            html_content = html_content.strip()
            
            # Validate that we have a complete HTML document
            if not html_content.startswith('<!DOCTYPE html>') and not html_content.startswith('<html'):
                # If not a complete document, wrap it
                html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generated Website</title>
</head>
<body>
{html_content}
</body>
</html>"""
            
            return html_content
        else:
            # More detailed error information
            error_details = []
            if not response:
                error_details.append("No response object returned")
            elif not hasattr(response, 'text'):
                error_details.append("Response object has no 'text' attribute")
            elif response.text is None:
                error_details.append("Response text is None")
            elif response.text == "":
                error_details.append("Response text is empty string")
            
            # Check for candidates and other response properties
            if hasattr(response, 'candidates'):
                error_details.append(f"Candidates: {len(response.candidates) if response.candidates else 0}")
                if response.candidates:
                    for i, candidate in enumerate(response.candidates):
                        if hasattr(candidate, 'finish_reason'):
                            error_details.append(f"Candidate {i} finish_reason: {candidate.finish_reason}")
            
            raise Exception(f"Empty response from Gemini AI. Details: {'; '.join(error_details)}")
            
    except Exception as e:
        print(f"Full error in website generation: {str(e)}")
        raise Exception(f"Error generating website with AI: {str(e)}")

def validate_html(html_content: str) -> bool:
    """
    Basic validation to ensure HTML content is well-formed.
    
    Args:
        html_content: HTML content to validate
    
    Returns:
        True if valid, False otherwise
    """
    try:
        # Basic checks
        required_tags = ['<html', '<head', '<body', '</html>', '</head>', '</body>']
        for tag in required_tags:
            if tag not in html_content:
                return False
        
        # Check for DOCTYPE
        if '<!DOCTYPE html>' not in html_content and '<!doctype html>' not in html_content.lower():
            return False
            
        return True
        
    except Exception:
        return False
