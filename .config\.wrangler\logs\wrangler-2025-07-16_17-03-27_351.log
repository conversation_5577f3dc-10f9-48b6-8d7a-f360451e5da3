
--- 2025-07-16T17:03:27.566Z debug
🪵  Writing logs to "/home/<USER>/workspace/.config/.wrangler/logs/wrangler-2025-07-16_17-03-27_351.log"
---

--- 2025-07-16T17:03:27.566Z debug
Metrics dispatcher: Posting data {"deviceId":"94cc17b3-0f80-4847-8c99-9fee1bd4e703","event":"wrangler command started","timestamp":1752685407565,"properties":{"amplitude_session_id":1752685407560,"amplitude_event_id":0,"wranglerVersion":"4.24.4","osPlatform":"Linux","osVersion":"#1-NixOS SMP PREEMPT_DYNAMIC Tue Jan  1 00:00:00 UTC 1980","nodeVersion":20,"packageManager":"npm","isFirstUsage":true,"configFileType":"none","isCI":false,"isPagesCI":false,"isWorkersCI":false,"isInteractive":false,"hasAssets":false,"argsUsed":["v","version"],"argsCombination":"v, version","command":"wrangler ","args":{"version":true,"v":true,"xJsonConfig":true,"j":true}}}
---

--- 2025-07-16T17:03:27.582Z debug
.env file not found at ".env". Continuing... For more details, refer to https://developers.cloudflare.com/workers/wrangler/system-environment-variables/
---

--- 2025-07-16T17:03:28.226Z log

 ⛅️ wrangler 4.24.4
[38;2;255;136;0m───────────────────[39m
---

--- 2025-07-16T17:03:28.229Z debug
Metrics dispatcher: Posting data {"deviceId":"94cc17b3-0f80-4847-8c99-9fee1bd4e703","event":"wrangler command completed","timestamp":1752685408229,"properties":{"amplitude_session_id":1752685407560,"amplitude_event_id":1,"wranglerVersion":"4.24.4","osPlatform":"Linux","osVersion":"#1-NixOS SMP PREEMPT_DYNAMIC Tue Jan  1 00:00:00 UTC 1980","nodeVersion":20,"packageManager":"npm","isFirstUsage":false,"configFileType":"none","isCI":false,"isPagesCI":false,"isWorkersCI":false,"isInteractive":false,"hasAssets":false,"argsUsed":["v","version"],"argsCombination":"v, version","command":"wrangler ","args":{"version":true,"v":true,"xJsonConfig":true,"j":true},"durationMs":696,"durationSeconds":0.696,"durationMinutes":0.0116}}
---
