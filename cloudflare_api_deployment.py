import os
import requests
import json
import uuid
import base64
from datetime import datetime
from typing import Dict, Any

class CloudflareAPIDeployment:
    """Direct Cloudflare API deployment without Wrangler"""
    
    def __init__(self):
        self.api_token = os.getenv("CLOUDFLARE_API_TOKEN")
        self.base_url = "https://api.cloudflare.com/client/v4"
        self.headers = {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json"
        }
    
    def get_account_id(self) -> str:
        """Get the actual account ID from environment or API"""
        try:
            # First try to get from environment variable
            account_id = os.getenv("CLOUDFLARE_ACCOUNT_ID")
            if account_id and len(account_id) == 32:  # Valid account ID format
                return account_id
                
            # Fallback to API call
            response = requests.get(f"{self.base_url}/accounts", headers=self.headers)
            if response.status_code == 200:
                data = response.json()
                if data.get("success") and data.get("result") and len(data["result"]) > 0:
                    return data["result"][0]["id"]
            return None
        except Exception:
            return None
    
    def deploy_website(self, html_content: str, name_hint: str = "") -> str:
        """Deploy HTML content using Cloudflare Pages API"""
        try:
            # Get real account ID
            account_id = self.get_account_id()
            if not account_id:
                return "❌ Could not retrieve Cloudflare account ID. Please check your API token permissions."
            
            # Generate project name
            deployment_id = str(uuid.uuid4())[:8]
            if name_hint:
                project_name = f"website-{name_hint.lower().replace(' ', '-')}-{deployment_id}"
            else:
                project_name = f"ai-website-{deployment_id}"
            
            # Create a simple deployment using direct upload
            result = self._upload_to_pages(account_id, project_name, html_content)
            
            if result["success"]:
                deployment_record = {
                    "deployment_id": deployment_id,
                    "project_name": project_name,
                    "url": result["url"],
                    "created_at": datetime.now().isoformat(),
                    "status": "deployed",
                    "html_size": len(html_content)
                }
                self._save_deployment_record(deployment_record)
                
                return f"✅ Website deployed successfully!\n🌐 Your live URL: {result['url']}\n📝 Project: {project_name}"
            else:
                return f"❌ Deployment failed: {result['error']}"
                
        except Exception as e:
            return f"❌ Deployment error: {str(e)}"
    
    def _upload_to_pages(self, account_id: str, project_name: str, html_content: str) -> Dict[str, Any]:
        """Upload content to Cloudflare Pages using file-based deployment"""
        try:
            # Create local deployment directory
            deploy_dir = f"./cloudflare-pages/{project_name}"
            os.makedirs(deploy_dir, exist_ok=True)
            
            # Write HTML content to index.html
            with open(f"{deploy_dir}/index.html", "w", encoding="utf-8") as f:
                f.write(html_content)
            
            # Use wrangler to deploy directly
            import subprocess
            cmd = [
                "npx", "wrangler", "pages", "deploy", deploy_dir,
                "--project-name", project_name
            ]
            
            env = os.environ.copy()
            env["CLOUDFLARE_ACCOUNT_ID"] = account_id
            env["CLOUDFLARE_API_TOKEN"] = self.api_token
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                env=env,
                timeout=120
            )
            
            if result.returncode == 0:
                # Extract URL from wrangler output
                output = result.stdout
                url = f"https://{project_name}.pages.dev"
                
                # Try to extract actual URL from output
                if "https://" in output:
                    lines = output.split('\n')
                    for line in lines:
                        if "https://" in line and "pages.dev" in line:
                            import re
                            url_match = re.search(r'https://[^\s]+\.pages\.dev', line)
                            if url_match:
                                found_url = url_match.group(0)
                                if found_url.count('.pages.dev') == 1:
                                    url = found_url
                                break
                
                return {
                    "success": True,
                    "url": url,
                    "project_name": project_name,
                    "output": output
                }
            else:
                return {
                    "success": False,
                    "error": result.stderr or result.stdout,
                    "output": result.stdout
                }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def _save_deployment_record(self, deployment_record: Dict[str, Any]):
        """Save deployment record to local file"""
        try:
            deployments_file = "cloudflare_deployments.json"
            
            if os.path.exists(deployments_file):
                with open(deployments_file, 'r') as f:
                    deployments = json.load(f)
            else:
                deployments = []
            
            deployments.append(deployment_record)
            
            with open(deployments_file, 'w') as f:
                json.dump(deployments, f, indent=2)
                
        except Exception as e:
            print(f"Warning: Could not save deployment record: {str(e)}")

def deploy_with_api(html_content: str, name_hint: str = "") -> str:
    """Deploy HTML content using Cloudflare API"""
    deployment = CloudflareAPIDeployment()
    return deployment.deploy_website(html_content, name_hint)