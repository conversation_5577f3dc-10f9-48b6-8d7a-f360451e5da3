<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - Full Stack Web Developer</title>
    <style>
        /* Color Variables */
        :root {
            --primary-color: #2563eb; /* Blue */
            --background-color: #f8fafc; /* Light Gray/Off-white */
            --text-color: #1e293b; /* Dark Blue/Gray */
            --secondary-text-color: #64748b; /* Slate Gray */
            --border-color: #e2e8f0; /* Light Gray */
        }

        /* Base Styles */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            max-width: 900px;
            margin: 30px auto;
            padding: 25px;
            background-color: #ffffff;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            border-radius: 8px;
        }

        /* Header Section */
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid var(--border-color);
        }

        .header h1 {
            color: var(--primary-color);
            margin: 0 0 5px;
            font-size: 2.8em;
            font-weight: 700;
        }

        .header h2 {
            color: var(--secondary-text-color);
            margin: 0 0 20px;
            font-size: 1.4em;
            font-weight: 500;
        }

        .contact-info {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px 30px;
            font-size: 0.95em;
        }

        .contact-info p {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .contact-info a {
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .contact-info a:hover {
            color: #1e40af; /* A darker shade of primary */
            text-decoration: underline;
        }

        /* Section Styling */
        section {
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px dashed var(--border-color);
        }

        section:last-of-type {
            border-bottom: none;
            margin-bottom: 0;
        }

        h3 {
            color: var(--primary-color);
            font-size: 1.8em;
            margin-bottom: 15px;
            border-bottom: 2px solid var(--primary-color);
            display: inline-block;
            padding-bottom: 5px;
        }
        
        h4 {
            color: var(--text-color);
            font-size: 1.25em;
            margin-top: 20px;
            margin-bottom: 8px;
        }

        p {
            margin-bottom: 10px;
        }

        ul {
            list-style-type: none;
            padding-left: 0;
        }

        ul li {
            position: relative;
            padding-left: 25px;
            margin-bottom: 8px;
        }

        ul li::before {
            content: '•';
            color: var(--primary-color);
            font-size: 1.2em;
            position: absolute;
            left: 0;
            top: 0;
        }

        /* Experience and Projects Specifics */
        .experience-item, .project-item {
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .experience-item:last-of-type, .project-item:last-of-type {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .job-title {
            display: flex;
            justify-content: space-between;
            align-items: baseline;
            flex-wrap: wrap;
            margin-bottom: 5px;
        }

        .job-title strong {
            font-size: 1.2em;
            color: var(--text-color);
        }

        .job-title span {
            color: var(--secondary-text-color);
            font-size: 0.95em;
            white-space: nowrap;
        }

        .project-details ul {
            margin-top: 10px;
            margin-bottom: 0;
        }

        .project-details ul li {
            font-size: 0.95em;
        }

        /* Skills Section */
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px 30px;
            margin-top: 15px;
        }

        .skill-category strong {
            color: var(--text-color);
            font-size: 1.1em;
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .skill-category ul {
            list-style-type: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .skill-category ul li {
            background-color: var(--primary-color);
            color: white;
            padding: 6px 12px;
            border-radius: 5px;
            font-size: 0.9em;
            white-space: nowrap;
            position: static; /* Override li::before positioning */
            padding-left: 12px; /* Remove padding for bullet */
        }
        .skill-category ul li::before {
            content: none;
        }

        /* Education and Certifications */
        .education-item, .certification-item {
            margin-bottom: 15px;
        }

        .education-item p, .certification-item p {
            margin: 0;
            font-size: 1em;
        }

        .education-item p:first-child {
            font-weight: 600;
            color: var(--text-color);
        }
        .education-item p:last-child {
            color: var(--secondary-text-color);
            font-size: 0.95em;
        }

        /* Icons (basic placeholder, could use Font Awesome for better icons) */
        .icon {
            font-size: 1.1em;
            margin-right: 5px;
            color: var(--primary-color);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                margin: 20px auto;
                padding: 20px;
            }

            .header h1 {
                font-size: 2.2em;
            }

            .header h2 {
                font-size: 1.2em;
            }

            .contact-info {
                flex-direction: column;
                align-items: center;
                gap: 10px;
            }

            h3 {
                font-size: 1.5em;
            }

            .job-title {
                flex-direction: column;
                align-items: flex-start;
            }

            .job-title span {
                margin-top: 5px;
            }

            .skills-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 480px) {
            .container {
                margin: 10px;
                padding: 15px;
                border-radius: 0;
                box-shadow: none;
            }

            .header h1 {
                font-size: 1.8em;
            }

            .header h2 {
                font-size: 1em;
            }

            h3 {
                font-size: 1.3em;
            }

            ul li {
                padding-left: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>Manzoor Ah Chopan</h1>
            <h2>Full Stack Web Developer</h2>
            <div class="contact-info">
                <p><span class="icon">📞</span> Phone: <a href="tel:7780883346">7780883346</a></p>
                <p><span class="icon">✉</span> Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                <p><span class="icon">📍</span> Address: Srinagar, Jammu & Kashmir</p>
                <p><span class="icon">🔗</span> LinkedIn: <a href="https://linkedin.com/in/manzoor-chopan-074b14201" target="_blank">linkedin.com/in/manzoor-chopan-074b14201</a></p>
                <p><span class="icon">🔗</span> GitHub: <a href="https://github.com/mchopan" target="_blank">github.com/mchopan</a></p>
                <p><span class="icon">🔗</span> Portfolio: <a href="https://mchopan.github.io/portfolio" target="_blank">mchopan.github.io/portfolio</a></p>
            </div>
        </header>

        <section id="summary">
            <h3>Professional Summary</h3>
            <p>Full Stack Web Developer with experience in designing and developing web applications using modern technologies. Proficient in front-end and back-end development, with a focus on creating user-friendly interfaces and scalable solutions. Adept at collaborating with cross-functional teams to deliver impactful digital experiences.</p>
        </section>

        <section id="experience">
            <h3>Professional Experience</h3>
            <div class="experience-item">
                <div class="job-title">
                    <strong>Cogveel Technologies | Full Stack Developer</strong>
                    <span>Sep 2023 – Present</span>
                </div>
                <ul>
                    <li>Developed and maintained web applications using React, Node.js, and MongoDB.</li>
                    <li>Built cross-platform mobile applications using React Native.</li>
                    <li>Collaborated with teams to design and implement new features.</li>
                    <li>Enhanced application performance and user experience.</li>
                </ul>
                <h4>Key Projects:</h4>
                <ul>
                    <li><strong>Neuquip:</strong> Solely developed the frontend of this AI-powered application with features including:
                        <ul>
                            <li>Uploading files (PDF, Excel, TXT, Word) and enabling conversational interaction with document content.</li>
                            <li>Generating analytics and exporting them into a sketchbook for document creation.</li>
                            <li>Creating workflows for sketchbooks, with sharing functionality among multiple users.</li>
                            <li>Designing an enhanced user interface to support complex workflows.</li>
                        </ul>
                    </li>
                    <li><strong>Apple Doc:</strong> Redesigned the UI and added new features to this orchid plant treatment app.</li>
                    <li><strong>Wabi Sabi:</strong> Contributed to the development of an e-commerce platform as part of a team.</li>
                    <li><strong>Quick Load:</strong> Created a booking app for trucks from scratch, handling both backend and frontend development.</li>
                </ul>
            </div>

            <div class="experience-item">
                <div class="job-title">
                    <strong>Red Stag Labs | Full Stack Developer</strong>
                    <span>Nov 2022 – May 2023</span>
                </div>
                <ul>
                    <li>Designed and developed a dynamic full-stack web application using ReactJS, Material-UI, CSS, Spring Boot, and MySQL.</li>
                    <li>Focused on creating user-friendly interfaces and enhancing interactivity.</li>
                    <li>Contributed to the project marvelminds.in.</li>
                </ul>
            </div>

            <div class="experience-item">
                <div class="job-title">
                    <strong>Oxford Skill and Education Institute | Web Developer</strong>
                    <span>Feb 2022 – Mar 2022</span>
                </div>
                <ul>
                    <li>Developed a comprehensive registration platform for students.</li>
                    <li>Improved the institute's online presence and streamlined the registration process.</li>
                </ul>
            </div>
        </section>

        <section id="projects">
            <h3>Personal Projects</h3>
            <div class="project-item">
                <h4>site-bot (NPM Package):</h4>
                <ul>
                    <li>Developed a highly customizable chatbot as an npm package designed to work seamlessly with any JavaScript framework or library.</li>
                    <li><strong>AI Integration:</strong> Utilizes an AI API key provided by the library user, enabling dynamic and context-aware interactions.</li>
                    <li><strong>Customizable and Flexible:</strong> Designed to be easily integrated into any website, providing developers with the flexibility to customize the chatbot's behavior and appearance.</li>
                    <li><strong>Cross-Platform Compatibility:</strong> Built with compatibility across modern JavaScript frameworks, making it adaptable for diverse project requirements.</li>
                    <li><strong>Developer-Friendly:</strong> Created with a focus on simplicity and ease of use, allowing developers to set up the chatbot with minimal configuration.</li>
                    <li><strong>Published on NPM:</strong> The package is published on npm, making it easily accessible for public use. (search in npm : mchopan/sitebot)</li>
                </ul>
            </div>
            <div class="project-item">
                <h4>Campus Command (React Native | Final Year Project):</h4>
                <ul>
                    <li>Designed and developed a messaging app tailored for educational institutions.</li>
                    <li>Enabled colleges to create semesters/groups and manage student registrations by registration numbers.</li>
                    <li>Built a student dashboard that displays only relevant semester/group data.</li>
                    <li>Integrated features like group chat, updates, and collaboration tools.</li>
                    <li>Solely responsible for the app's design, development, and deployment.</li>
                </ul>
            </div>
            <div class="project-item">
                <h4>Other Projects:</h4>
                <ul>
                    <li>Portfolio Website: Showcased professional skills and projects.</li>
                    <li>CASET College Website: Built a dynamic website for the college.</li>
                    <li>Mehndi Artist Portfolio: Designed an elegant portfolio for a mehndi artist.</li>
                    <li>News Website: Developed a news website leveraging the News API.</li>
                    <li>Facebook Clone: Built a front-end clone of Facebook.</li>
                    <li>Itinerary Website: Created a website for booking travel-related services.</li>
                </ul>
            </div>
        </section>

        <section id="education">
            <h3>Education</h3>
            <div class="education-item">
                <p><strong>Caset College of Computer Science</strong></p>
                <p>Bachelor of Computer Applications (BCA) | 2021 – 2023</p>
            </div>
            <div class="education-item">
                <p><strong>Govt Boys Higher Secondary (JKBOSE)</strong></p>
                <p>2019 – 2020</p>
            </div>
            <div class="education-item">
                <p><strong>Maulana Azad Education Foundation</strong></p>
                <p>Web Designing & Publishing Assistant | 2017 – 2018</p>
            </div>
            <div class="education-item">
                <p><strong>Govt Boys High School (JKBOSE)</strong></p>
                <p>2016 – 2017</p>
            </div>
        </section>

        <section id="skills">
            <h3>Skills</h3>
            <div class="skills-grid">
                <div class="skill-category">
                    <strong>Front-End:</strong>
                    <ul>
                        <li>React</li>
                        <li>HTML</li>
                        <li>CSS</li>
                        <li>JavaScript</li>
                        <li>Material-UI</li>
                    </ul>
                </div>
                <div class="skill-category">
                    <strong>Back-End:</strong>
                    <ul>
                        <li>Node.js</li>
                        <li>Spring Boot</li>
                        <li>MySQL</li>
                        <li>MongoDB</li>
                    </ul>
                </div>
                <div class="skill-category">
                    <strong>Mobile Development:</strong>
                    <ul>
                        <li>React Native</li>
                    </ul>
                </div>
                <div class="skill-category">
                    <strong>Other:</strong>
                    <ul>
                        <li>Git</li>
                        <li>Web Designing & Publishing</li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="certifications">
            <h3>Certifications</h3>
            <ul>
                <li class="certification-item">Full Stack Web Development – Red Stag Labs, Qamarwari, Srinagar</li>
                <li class="certification-item">Linux Fundamentals – Kimo.ai</li>
                <li class="certification-item">Hands-On Linux System Administration – Kimo.ai</li>
                <li class="certification-item">Data Structures and Algorithms – Kimo.ai</li>
            </ul>
        </section>
    </div>
</body>
</html>