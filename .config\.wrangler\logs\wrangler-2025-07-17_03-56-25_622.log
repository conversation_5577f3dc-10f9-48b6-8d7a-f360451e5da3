
--- 2025-07-17T03:56:25.826Z debug
🪵  Writing logs to "/home/<USER>/workspace/.config/.wrangler/logs/wrangler-2025-07-17_03-56-25_622.log"
---

--- 2025-07-17T03:56:25.826Z debug
Metrics dispatcher: Posting data {"deviceId":"94cc17b3-0f80-4847-8c99-9fee1bd4e703","event":"wrangler command started","timestamp":1752724585826,"properties":{"amplitude_session_id":1752724585823,"amplitude_event_id":0,"wranglerVersion":"4.24.4","osPlatform":"Linux","osVersion":"#1-NixOS SMP PREEMPT_DYNAMIC Tue Jan  1 00:00:00 UTC 1980","nodeVersion":20,"packageManager":"npm","isFirstUsage":false,"configFileType":"none","isCI":false,"isPagesCI":false,"isWorkersCI":false,"isInteractive":false,"hasAssets":false,"argsUsed":["compatibilityDate","j","projectName"],"argsCombination":"compatibilityDate, j, projectName","command":"wrangler pages deploy","args":{"projectName":"<REDACTED>","compatibilityDate":"<REDACTED>","xJsonConfig":true,"j":true,"uploadSourceMaps":false,"directory":"<REDACTED>"}}}
---

--- 2025-07-17T03:56:25.840Z log

---

--- 2025-07-17T03:56:26.204Z error
[31m✘ [41;31m[[41;97mERROR[41;31m][0m [1mUnknown arguments: compatibility-date, compatibilityDate[0m


---

--- 2025-07-17T03:56:26.245Z debug
Metrics dispatcher: Posting data {"deviceId":"94cc17b3-0f80-4847-8c99-9fee1bd4e703","event":"wrangler command errored","timestamp":1752724586245,"properties":{"amplitude_session_id":1752724585823,"amplitude_event_id":1,"wranglerVersion":"4.24.4","osPlatform":"Linux","osVersion":"#1-NixOS SMP PREEMPT_DYNAMIC Tue Jan  1 00:00:00 UTC 1980","nodeVersion":20,"packageManager":"npm","isFirstUsage":false,"configFileType":"none","isCI":false,"isPagesCI":false,"isWorkersCI":false,"isInteractive":false,"hasAssets":false,"argsUsed":["compatibilityDate","j","projectName"],"argsCombination":"compatibilityDate, j, projectName","command":"wrangler pages deploy","args":{"projectName":"<REDACTED>","compatibilityDate":"<REDACTED>","xJsonConfig":true,"j":true,"uploadSourceMaps":false,"directory":"<REDACTED>"},"durationMs":451,"durationSeconds":0.451,"durationMinutes":0.007516666666666667,"errorType":"CommandLineArgsError","errorMessage":"yargs validation error"}}
---
