import fitz
import os
import subprocess
from typing import TypedDict, Annotated, List
from langgraph.graph.message import add_messages
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import SystemMessage, HumanMessage, BaseMessage
from langgraph.prebuilt import ToolNode, tools_condition
from langchain_core.tools import tool
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import InMemorySaver
import json
import uuid
import random
import string
from datetime import datetime
import time

# ---------------------- TOOLS ----------------------

# User database (in production, use a real database)
USERS_DB_FILE = "users_db.json"

def load_users_db():
    """Load users database from file"""
    if os.path.exists(USERS_DB_FILE):
        try:
            with open(USERS_DB_FILE, 'r') as f:
                return json.load(f)
        except:
            return {}
    return {}

def save_users_db(users_db):
    """Save users database to file"""
    with open(USERS_DB_FILE, 'w') as f:
        json.dump(users_db, f, indent=2)

@tool
def auto_generate_user_id(name_hint: str = "") -> str:
    """
    Automatically generate a unique user ID for deployment.
    This runs in the background when user wants to publish.
    
    Args:
        name_hint: Optional hint from user content (like name from resume)
    
    Returns:
        Generated user ID
    """
    users_db = load_users_db()
    
    if name_hint:
        # Create ID from name hint
        base_name = name_hint.split()[0].lower().replace(" ", "")[:8]  # First 8 chars of first name
        random_num = random.randint(100, 999)
        user_id = f"{base_name}{random_num}"
    else:
        # Generate random user ID
        user_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
    
    # Ensure uniqueness
    while user_id in users_db:
        random_num = random.randint(100, 999)
        if name_hint:
            user_id = f"{base_name}{random_num}"
        else:
            user_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
    
    # Create simple user profile
    user_profile = {
        "user_id": user_id,
        "created_at": datetime.now().isoformat(),
        "projects": [],
        "last_activity": datetime.now().isoformat()
    }
    
    # Save to database
    users_db[user_id] = user_profile
    save_users_db(users_db)
    
    return user_id

@tool
def process_resume_file(file_path: str) -> str:
    """
    Process uploaded resume/CV file to extract information for portfolio website.
    Supports PDF and text files.
    
    Args:
        file_path: Path to the uploaded resume file
    
    Returns:
        Extracted information formatted for website creation
    """
    try:
        if file_path.lower().endswith('.pdf'):
            # Extract text from PDF
            doc = fitz.open(file_path)
            text = ""
            for page in doc:
                text += page.get_text()
            doc.close()
        else:
            # Handle text files
            with open(file_path, 'r', encoding='utf-8') as f:
                text = f.read()
        
        # Basic information extraction (you can enhance this with NLP)
        info = {
            "raw_text": text,
            "length": len(text),
            "has_email": "@" in text,
            "has_phone": any(char.isdigit() for char in text),
            "sections": []
        }
        
        # Try to identify common resume sections
        common_sections = [
            "experience", "education", "skills", "projects", 
            "summary", "objective", "certifications", "achievements"
        ]
        
        text_lower = text.lower()
        for section in common_sections:
            if section in text_lower:
                info["sections"].append(section)
        
        result = f"📄 Resume/CV processed successfully!\n\n"
        result += f"📊 Content Analysis:\n"
        result += f"   - Length: {info['length']} characters\n"
        result += f"   - Contains email: {'✅' if info['has_email'] else '❌'}\n"
        result += f"   - Contains phone: {'✅' if info['has_phone'] else '❌'}\n"
        result += f"   - Detected sections: {', '.join(info['sections']) if info['sections'] else 'None detected'}\n\n"
        result += f"📝 Raw content:\n{text[:500]}{'...' if len(text) > 500 else ''}\n\n"
        result += f"Now I'll create a professional portfolio website based on this information!"
        
        return result
        
    except Exception as e:
        return f"❌ Error processing resume file: {str(e)}"

@tool
def process_instruction_file(file_path: str) -> str:
    """
    Process uploaded instruction file for specific website requirements.
    
    Args:
        file_path: Path to the uploaded instruction file
    
    Returns:
        Processed instructions for website creation
    """
    try:
        if file_path.lower().endswith('.pdf'):
            # Extract text from PDF
            doc = fitz.open(file_path)
            text = ""
            for page in doc:
                text += page.get_text()
            doc.close()
        else:
            # Handle text files
            with open(file_path, 'r', encoding='utf-8') as f:
                text = f.read()
        
        result = f"📋 Instructions processed successfully!\n\n"
        result += f"📝 Your requirements:\n{text}\n\n"
        result += f"I'll create a website following these specific instructions!"
        
        return result
        
    except Exception as e:
        return f"❌ Error processing instruction file: {str(e)}"

@tool
def generate_website_html(content: str, website_type: str = "Portfolio/Resume", color_scheme: str = "Professional Blue") -> str:
    """
    Generate complete HTML website using Google Gemini AI.
    
    Args:
        content: Processed content from resume or instructions
        website_type: Type of website to generate
        color_scheme: Color scheme for the website
    
    Returns:
        Complete HTML content
    """
    from utils.website_generator import generate_website_with_ai
    
    try:
        html_content = generate_website_with_ai(content, website_type, color_scheme)
        return f"✅ Website generated successfully!\n\nHere's your HTML website:\n\n{html_content}"
    except Exception as e:
        return f"❌ Error generating website: {str(e)}"

@tool
def deploy_html_to_cloudflare(html_content: str, name_hint: str = "") -> str:
    """
    Deploy HTML to Cloudflare Pages with auto-generated user ID.
    
    Args:
        html_content: The HTML content to deploy
        name_hint: Optional hint for generating user ID (from resume or user input)
    
    Returns:
        Deployment confirmation with generated URL
    """
    # Auto-generate user ID
    user_id = auto_generate_user_id(name_hint)
    
    # For MVP, use mock deployment
    from utils.deployment import mock_deploy_website
    try:
        mock_url = mock_deploy_website(html_content)
        
        # Update user's project history
        users_db = load_users_db()
        if user_id in users_db:
            project = {
                "name": f"Website Deployment",
                "url": mock_url,
                "created_at": datetime.now().isoformat()
            }
            users_db[user_id]["projects"].append(project)
            users_db[user_id]["last_activity"] = datetime.now().isoformat()
            save_users_db(users_db)
        
        return f"✅ Website deployed successfully!\n🌐 Your URL: {mock_url}\n📝 Your unique ID: {user_id} (save this for future reference)"
        
    except Exception as e:
        return f"❌ Deployment failed: {str(e)}"

# ---------------------- TOOLS LIST ----------------------

tools = [
    auto_generate_user_id,
    process_resume_file, 
    process_instruction_file,
    generate_website_html,
    deploy_html_to_cloudflare
]

# ---------------------- AGENT STATE ----------------------

class AgentState(TypedDict):
    messages: Annotated[List[BaseMessage], add_messages]
    html_content: str
    user_satisfied: bool
    awaiting_publish_confirmation: bool
    user_id: str
    resume_processed: bool
    website_created: bool

# ---------------------- LLM ----------------------

def get_llm():
    """Get LLM instance with tools"""
    return ChatGoogleGenerativeAI(
        model="gemini-2.5-flash",  # Use faster, more efficient model
        google_api_key=os.getenv("GEMINI_API_KEY"),
        temperature=0.7,
        max_retries=2,
        request_timeout=30
    ).bind_tools(tools)

# Enhanced system prompt for proper workflow
DEFAULT_PROMPT = """
You are an expert frontend developer AI assistant that helps users create beautiful, responsive HTML pages.

Your workflow:
1. Understand what the user wants:
   - Resume/CV portfolio website
   - Custom website with specific requirements
   - Direct description of their needs

2. Process their content efficiently using the appropriate tools

3. Generate complete HTML using generate_website_html tool with professional styling

4. Show a brief summary of what you created and ask for feedback

5. Make revisions if needed

6. Ask for deployment confirmation once satisfied

IMPORTANT RULES:
- Be concise and efficient in responses
- Only use tools when necessary to avoid rate limits
- Always ask "Are you satisfied with this website?" before deployment
- Never deploy without explicit user confirmation
- Create complete, functional HTML pages
- Focus on user needs rather than technical details

Keep responses short and focused. Avoid lengthy explanations unless asked.
"""

# ---------------------- LLM MODEL CALL NODE ----------------------

def llm_model_call(state: AgentState) -> AgentState:
    """LLM model call with tools"""
    llm = get_llm()
    
    system_prompt = os.getenv("SYSTEM_PROMPT", DEFAULT_PROMPT)

    valid_messages = [msg for msg in state["messages"] if getattr(msg, "content", "").strip()]
    response = llm.invoke([
        SystemMessage(content=system_prompt),
        *valid_messages
    ])
    
    # Check if response contains HTML (look for complete HTML structure)
    has_html = "<html>" in response.content and "</html>" in response.content
    
    # Check if asking for satisfaction
    asking_satisfaction = any(phrase in response.content.lower() for phrase in [
        "satisfied", "happy with", "ready to publish", "want to publish", 
        "are you satisfied", "would you like me to make any changes"
    ])
    
    return {
        "messages": state["messages"] + [response],
        "html_content": response.content if has_html else state.get("html_content", ""),
        "user_satisfied": state.get("user_satisfied", False),
        "awaiting_publish_confirmation": asking_satisfaction and has_html,
        "user_id": state.get("user_id", ""),
        "resume_processed": state.get("resume_processed", False),
        "website_created": has_html or state.get("website_created", False)
    }

# ---------------------- TOOL NODE ----------------------

tool_node = ToolNode(tools)

# ---------------------- DECISION FUNCTIONS ----------------------

def should_continue(state: AgentState) -> str:
    """Determine next step based on state"""
    last_message = state["messages"][-1]
    
    # Check if LLM wants to use a tool
    if hasattr(last_message, "tool_calls") and last_message.tool_calls:
        tool_name = last_message.tool_calls[0]["name"]
        
        # Only allow deployment if user is satisfied
        if tool_name == "deploy_html_to_cloudflare":
            if state.get("user_satisfied") and state.get("awaiting_publish_confirmation"):
                return "tool_node"
            else:
                return "continue"
        
        return "tool_node"
    
    # Continue conversation
    return "continue"

# ---------------------- AGENT CLASS ----------------------

class WebsiteGeneratorAgent:
    """LangGraph-based website generator agent"""
    
    def __init__(self):
        self.memory = InMemorySaver()
        self.graph = self._build_graph()
        self.config = {"configurable": {"thread_id": "streamlit_session"}}
    
    def _build_graph(self):
        """Build the LangGraph"""
        graph_builder = StateGraph(AgentState)
        
        # Add nodes
        graph_builder.add_node("llm_call", llm_model_call)
        graph_builder.add_node("tool_node", tool_node)
        
        # Set entry point
        graph_builder.set_entry_point("llm_call")
        
        # Add edges
        graph_builder.add_conditional_edges(
            "llm_call",
            should_continue,
            {
                "tool_node": "tool_node",
                "continue": END
            }
        )
        
        graph_builder.add_edge("tool_node", "llm_call")
        
        # Compile graph
        return graph_builder.compile(checkpointer=self.memory)
    
    def process_message(self, user_message: str, current_state: dict = None) -> dict:
        """Process a user message and return agent response"""
        if current_state is None:
            current_state = {
                "messages": [HumanMessage(content=user_message)],
                "html_content": "",
                "user_satisfied": False,
                "awaiting_publish_confirmation": False,
                "user_id": "",
                "resume_processed": False,
                "website_created": False
            }
        else:
            current_state["messages"].append(HumanMessage(content=user_message))
        
        # Check for satisfaction/publishing responses
        satisfaction_keywords = ["satisfied", "happy", "good", "perfect", "looks good", "great"]
        publish_keywords = ["publish", "deploy", "go live", "yes", "yeah", "sure"]
        change_keywords = ["change", "modify", "edit", "different", "no", "not satisfied"]
        
        user_satisfied = any(keyword in user_message.lower() for keyword in satisfaction_keywords)
        wants_to_publish = any(keyword in user_message.lower() for keyword in publish_keywords)
        wants_changes = any(keyword in user_message.lower() for keyword in change_keywords)
        
        # Determine publishing intent
        if current_state.get("awaiting_publish_confirmation"):
            if wants_to_publish and not wants_changes:
                current_state["user_satisfied"] = True
            elif wants_changes:
                current_state["user_satisfied"] = False
        
        # Invoke the graph
        result = self.graph.invoke(current_state, config=self.config)
        
        return result
    
    def get_last_message(self, state: dict) -> str:
        """Get the last AI message from state"""
        if state and state.get("messages"):
            last_msg = state["messages"][-1]
            if hasattr(last_msg, "content"):
                return last_msg.content
        return ""

# ---------------------- SINGLETON INSTANCE ----------------------

_agent_instance = None

def get_agent():
    """Get singleton agent instance"""
    global _agent_instance
    if _agent_instance is None:
        _agent_instance = WebsiteGeneratorAgent()
    return _agent_instance