
--- 2025-07-17T03:59:27.600Z debug
🪵  Writing logs to "/home/<USER>/workspace/.config/.wrangler/logs/wrangler-2025-07-17_03-59-27_391.log"
---

--- 2025-07-17T03:59:27.600Z debug
Metrics dispatcher: Posting data {"deviceId":"94cc17b3-0f80-4847-8c99-9fee1bd4e703","event":"wrangler command started","timestamp":1752724767599,"properties":{"amplitude_session_id":*************,"amplitude_event_id":0,"wranglerVersion":"4.24.4","osPlatform":"Linux","osVersion":"#1-NixOS SMP PREEMPT_DYNAMIC Tue Jan  1 00:00:00 UTC 1980","nodeVersion":20,"packageManager":"npm","isFirstUsage":false,"configFileType":"none","isCI":false,"isPagesCI":false,"isWorkersCI":false,"isInteractive":false,"hasAssets":false,"argsUsed":[],"argsCombination":"","command":"wrangler whoami","args":{"xJsonConfig":true,"j":true}}}
---

--- 2025-07-17T03:59:27.616Z debug
.env file not found at ".env". Continuing... For more details, refer to https://developers.cloudflare.com/workers/wrangler/system-environment-variables/
---

--- 2025-07-17T03:59:27.653Z log

 ⛅️ wrangler 4.24.4
[38;2;255;136;0m───────────────────[39m
---

--- 2025-07-17T03:59:27.660Z log
Getting User settings...
---

--- 2025-07-17T03:59:27.662Z debug
-- START CF API REQUEST: GET https://api.cloudflare.com/client/v4/user/tokens/verify
---

--- 2025-07-17T03:59:27.662Z debug
QUERY STRING: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T03:59:27.662Z debug
HEADERS: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T03:59:27.662Z debug
INIT: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T03:59:27.662Z debug
-- END CF API REQUEST
---

--- 2025-07-17T03:59:27.907Z debug
-- START CF API RESPONSE: OK 200
---

--- 2025-07-17T03:59:27.907Z debug
HEADERS: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T03:59:27.907Z debug
RESPONSE: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T03:59:27.907Z debug
-- END CF API RESPONSE
---

--- 2025-07-17T03:59:27.909Z debug
-- START CF API REQUEST: GET https://api.cloudflare.com/client/v4/user
---

--- 2025-07-17T03:59:27.910Z debug
QUERY STRING: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T03:59:27.910Z debug
HEADERS: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T03:59:27.910Z debug
INIT: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T03:59:27.910Z debug
-- END CF API REQUEST
---

--- 2025-07-17T03:59:28.168Z debug
-- START CF API RESPONSE: Forbidden 403
---

--- 2025-07-17T03:59:28.168Z debug
HEADERS: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T03:59:28.168Z debug
RESPONSE: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T03:59:28.168Z debug
-- END CF API RESPONSE
---

--- 2025-07-17T03:59:28.170Z debug
-- START CF API REQUEST: GET https://api.cloudflare.com/client/v4/accounts
---

--- 2025-07-17T03:59:28.170Z debug
QUERY STRING: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T03:59:28.170Z debug
HEADERS: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T03:59:28.170Z debug
INIT: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T03:59:28.170Z debug
-- END CF API REQUEST
---

--- 2025-07-17T03:59:28.390Z debug
-- START CF API RESPONSE: OK 200
---

--- 2025-07-17T03:59:28.390Z debug
HEADERS: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T03:59:28.390Z debug
RESPONSE: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T03:59:28.390Z debug
-- END CF API RESPONSE
---

--- 2025-07-17T03:59:28.391Z log
👋 You are logged in with an User API Token. Unable to retrieve email for this user. Are you missing the `User->User Details->Read` permission?
---

--- 2025-07-17T03:59:28.391Z log
ℹ️  The API Token is read from the CLOUDFLARE_API_TOKEN environment variable.
---

--- 2025-07-17T03:59:28.392Z log

---

--- 2025-07-17T03:59:28.392Z log
🔓 To see token permissions visit https://dash.cloudflare.com/profile/api-tokens.
---

--- 2025-07-17T03:59:28.393Z debug
Metrics dispatcher: Posting data {"deviceId":"94cc17b3-0f80-4847-8c99-9fee1bd4e703","event":"view accounts","timestamp":*************,"properties":{"category":"Workers","wranglerVersion":"4.24.4","os":"linux:x64"}}
---

--- 2025-07-17T03:59:28.403Z debug
Metrics dispatcher: Posting data {"deviceId":"94cc17b3-0f80-4847-8c99-9fee1bd4e703","event":"wrangler command completed","timestamp":*************,"properties":{"amplitude_session_id":*************,"amplitude_event_id":1,"wranglerVersion":"4.24.4","osPlatform":"Linux","osVersion":"#1-NixOS SMP PREEMPT_DYNAMIC Tue Jan  1 00:00:00 UTC 1980","nodeVersion":20,"packageManager":"npm","isFirstUsage":false,"configFileType":"none","isCI":false,"isPagesCI":false,"isWorkersCI":false,"isInteractive":false,"hasAssets":false,"argsUsed":[],"argsCombination":"","command":"wrangler whoami","args":{"xJsonConfig":true,"j":true},"durationMs":826,"durationSeconds":0.826,"durationMinutes":0.013766666666666667}}
---
