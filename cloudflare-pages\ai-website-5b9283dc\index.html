<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - Full Stack Web Developer</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        /* CSS Variables for consistent styling */
        :root {
            --primary-color: #2563eb; /* Blue */
            --background-color: #f8fafc; /* Light Grey/Off-white */
            --text-color: #1e293b; /* Dark Blue/Grey */
            --container-bg-color: #ffffff; /* White */
            --border-color: #e2e8f0; /* Light border grey */
            --light-blue-bg: #e0f2fe; /* For skill tags */
            --dark-blue-text: #0c4a6e; /* For skill tag text */
            --secondary-text-color: #64748b; /* For dates/less prominent text */
            --border-radius: 8px;
            --section-spacing: 2.5rem;
            --line-height: 1.6;
        }

        /* Base Styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: var(--line-height);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: 20px; /* Padding around the main container */
        }

        /* Main Container */
        .container {
            max-width: 900px;
            margin: 20px auto; /* Center the container with vertical margin */
            background-color: var(--container-bg-color);
            border-radius: var(--border-radius);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05); /* Subtle shadow */
            padding: 2.5rem 3rem; /* Generous padding inside */
        }

        /* Header Section */
        .header {
            text-align: center;
            margin-bottom: var(--section-spacing);
            padding-bottom: 1.5rem;
            border-bottom: 1px solid var(--border-color); /* Separator line */
        }

        .header h1 {
            font-size: 2.8rem;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            font-weight: 700;
            letter-spacing: -0.025em; /* Slight letter spacing adjustment */
        }

        .header h2 {
            font-size: 1.5rem;
            color: var(--text-color);
            margin-bottom: 1.5rem;
            font-weight: 400;
        }

        .contact-info, .social-links {
            display: flex;
            flex-wrap: wrap; /* Allow items to wrap on smaller screens */
            justify-content: center;
            gap: 1.2rem; /* Space between contact/social items */
            margin-bottom: 1.2rem;
            font-size: 0.95rem;
        }

        .contact-info span, .social-links a {
            display: flex;
            align-items: center; /* Align text and icon vertically */
            color: var(--text-color);
            text-decoration: none;
            transition: color 0.2s ease-in-out; /* Smooth hover effect */
        }

        .contact-info span:hover, .social-links a:hover {
            color: var(--primary-color);
        }

        /* Emojis for contact/social links */
        .contact-info span::before, .social-links a::before {
            margin-right: 0.4em; /* Space between emoji and text */
        }
        .contact-info span:nth-child(1)::before { content: '📞'; } /* Phone */
        .contact-info span:nth-child(2)::before { content: '✉'; } /* Email */
        .contact-info span:nth-child(3)::before { content: '📍'; } /* Address */
        .social-links a:nth-child(1)::before { content: '🔗'; } /* LinkedIn */
        .social-links a:nth-child(2)::before { content: '🔗'; } /* GitHub */
        .social-links a:nth-child(3)::before { content: '🔗'; } /* Portfolio */


        /* Section Styling */
        section {
            margin-bottom: var(--section-spacing);
            padding-top: 1.5rem; /* Space above each section content */
        }

        section h2 {
            font-size: 1.8rem;
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            font-weight: 600;
            border-bottom: 2px solid var(--primary-color); /* Underline effect */
            display: inline-block; /* Make border only as wide as text */
            padding-bottom: 0.3rem;
        }

        /* Professional Summary */
        .summary p {
            font-size: 1.05rem;
            line-height: 1.7;
            margin-bottom: 1rem;
        }

        /* Experience & Projects Items */
        .experience-item, .project-item {
            margin-bottom: 2rem;
            padding-bottom: 1.5rem;
            border-bottom: 1px dashed var(--border-color); /* Dashed separator */
        }
        .experience-item:last-child, .project-item:last-child {
            border-bottom: none; /* No border for the last item in a section */
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .experience-header, .project-header {
            display: flex;
            justify-content: space-between;
            align-items: baseline; /* Align text baselines */
            margin-bottom: 0.5rem;
            flex-wrap: wrap; /* Allow wrapping for responsiveness */
        }

        .experience-header h3, .project-header h3 {
            font-size: 1.25rem;
            color: var(--text-color);
            font-weight: 600;
            margin-right: 1rem; /* Space between title and date */
        }
        .experience-header h3 span, .project-header h3 span {
            font-weight: 400; /* For role/type */
            color: var(--secondary-text-color);
            font-size: 1.1rem;
        }

        .experience-header .duration, .project-header .type {
            font-size: 0.95rem;
            color: var(--secondary-text-color);
            white-space: nowrap; /* Prevent dates from breaking */
        }

        /* List Styling (General) */
        .experience-item ul, .project-item ul, .education-list, .certifications-list {
            list-style: none; /* Remove default bullets */
            padding-left: 0;
            margin-top: 0.8rem;
        }

        .experience-item ul li, .project-item ul li,
        .education-list li, .certifications-list li {
            position: relative;
            padding-left: 1.5rem; /* Space for custom bullet */
            margin-bottom: 0.6rem;
            font-size: 1rem;
        }

        .experience-item ul li::before, .project-item ul li::before,
        .education-list li::before, .certifications-list li::before {
            content: '•'; /* Custom bullet point */
            color: var(--primary-color);
            font-weight: bold;
            position: absolute;
            left: 0;
            top: 0;
            font-size: 1.2rem;
            line-height: 1; /* Align bullet with text */
        }
        
        /* Nested Lists (for Key Projects, AI Integration details) */
        .experience-item ul li ul, .project-item ul li ul {
            margin-top: 0.5rem;
            margin-bottom: 0.5rem;
            padding-left: 1rem; /* Indent nested list */
            list-style: none;
        }
        .experience-item ul li ul li, .project-item ul li ul li {
            padding-left: 1.5rem;
            margin-bottom: 0.4rem;
        }
        .experience-item ul li ul li::before, .project-item ul li ul li::before {
            content: '–'; /* Different bullet for nested items */
            color: var(--secondary-text-color);
            font-size: 1rem;
            top: 0.1em;
        }

        /* Skills Section (as tags) */
        .skills-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px; /* Space between skill tags */
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .skills-list li {
            background-color: var(--light-blue-bg);
            color: var(--dark-blue-text);
            padding: 0.4rem 0.8rem;
            border-radius: 4px;
            font-size: 0.9rem;
            font-weight: 500;
            white-space: nowrap; /* Prevent tags from breaking */
            position: static; /* Override general li positioning */
            padding-left: 0.8rem; /* Remove extra padding from general li */
        }
        .skills-list li::before {
            content: none; /* Remove custom bullet for skill tags */
        }

        /* Education & Certifications Specifics */
        .education-list li strong {
            color: var(--text-color);
            font-weight: 600;
        }
        .education-list li span {
            color: var(--secondary-text-color);
            font-size: 0.95rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 2rem;
                margin: 10px auto; /* Reduce vertical margin on smaller screens */
            }

            .header h1 {
                font-size: 2.2rem;
            }

            .header h2 {
                font-size: 1.2rem;
            }

            .contact-info, .social-links {
                flex-direction: column; /* Stack contact/social items vertically */
                align-items: center;
                gap: 0.8rem;
            }

            section h2 {
                font-size: 1.5rem;
                margin-bottom: 1rem;
            }

            .experience-header, .project-header {
                flex-direction: column; /* Stack title/date vertically */
                align-items: flex-start;
                gap: 0.2rem;
            }

            .experience-header .duration, .project-header .type {
                margin-top: 0.2rem;
            }

            .experience-item ul li, .project-item ul li,
            .education-list li, .skills-list li, .certifications-list li {
                font-size: 0.95rem; /* Slightly smaller text */
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 10px;
            }
            .container {
                padding: 1.5rem;
            }
            .header h1 {
                font-size: 1.8rem;
            }
            .header h2 {
                font-size: 1rem;
            }
            .contact-info span, .social-links a {
                font-size: 0.9rem;
            }
            section h2 {
                font-size: 1.3rem;
            }
            .experience-item ul li, .project-item ul li,
            .education-list li, .skills-list li, .certifications-list li {
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>Manzoor Ah Chopan</h1>
            <h2>Full Stack Web Developer</h2>
            <div class="contact-info">
                <span>7780883346</span>
                <span><EMAIL></span>
                <span>Srinagar, Jammu & Kashmir</span>
            </div>
            <div class="social-links">
                <a href="https://linkedin.com/in/manzoor-chopan-074b14201" target="_blank" rel="noopener noreferrer">LinkedIn</a>
                <a href="https://github.com/mchopan" target="_blank" rel="noopener noreferrer">GitHub</a>
                <a href="https://mchopan.github.io/portfolio" target="_blank" rel="noopener noreferrer">Portfolio</a>
            </div>
        </header>

        <section class="summary">
            <h2>Professional Summary</h2>
            <p>Full Stack Web Developer with experience in designing and developing web applications using modern technologies. Proficient in front-end and back-end development, with a focus on creating user-friendly interfaces and scalable solutions. Adept at collaborating with cross-functional teams to deliver impactful digital experiences.</p>
        </section>

        <section class="experience">
            <h2>Professional Experience</h2>

            <div class="experience-item">
                <div class="experience-header">
                    <h3>Cogveel Technologies <span>| Full Stack Developer</span></h3>
                    <span class="duration">Sep 2023 – Present</span>
                </div>
                <ul>
                    <li>Developed and maintained web applications using React, Node.js, and MongoDB.</li>
                    <li>Built cross-platform mobile applications using React Native.</li>
                    <li>Collaborated with teams to design and implement new features.</li>
                    <li>Enhanced application performance and user experience.</li>
                    <li>Key Projects:
                        <ul>
                            <li>Neuquip: Solely developed the frontend of this AI-powered application with features including:
                                <ul>
                                    <li>Uploading files (PDF, Excel, TXT, Word) and enabling conversational interaction with document content.</li>
                                    <li>Generating analytics and exporting them into a sketchbook for document creation.</li>
                                    <li>Creating workflows for sketchbooks, with sharing functionality among multiple users.</li>
                                    <li>Designing an enhanced user interface to support complex workflows.</li>
                                </ul>
                            </li>
                            <li>Apple Doc: Redesigned the UI and added new features to this orchid plant treatment app.</li>
                            <li>Wabi Sabi: Contributed to the development of an e-commerce platform as part of a team.</li>
                            <li>Quick Load: Created a booking app for trucks from scratch, handling both backend and frontend development.</li>
                        </ul>
                    </li>
                </ul>
            </div>

            <div class="experience-item">
                <div class="experience-header">
                    <h3>Red Stag Labs <span>| Full Stack Developer</span></h3>
                    <span class="duration">Nov 2022 – May 2023</span>
                </div>
                <ul>
                    <li>Designed and developed a dynamic full-stack web application using ReactJS, Material-UI, CSS, Spring Boot, and MySQL.</li>
                    <li>Focused on creating user-friendly interfaces and enhancing interactivity.</li>
                    <li>Contributed to the project marvelminds.in.</li>
                </ul>
            </div>

            <div class="experience-item">
                <div class="experience-header">
                    <h3>Oxford Skill and Education Institute <span>| Web Developer</span></h3>
                    <span class="duration">Feb 2022 – Mar 2022</span>
                </div>
                <ul>
                    <li>Developed a comprehensive registration platform for students.</li>
                    <li>Improved the institute's online presence and streamlined the registration process.</li>
                </ul>
            </div>
        </section>

        <section class="projects">
            <h2>Personal Projects</h2>

            <div class="project-item">
                <div class="project-header">
                    <h3>site-bot <span>(NPM Package)</span></h3>
                </div>
                <ul>
                    <li>Developed a highly customizable chatbot as an npm package designed to work seamlessly with any JavaScript framework or library.
                        <ul>
                            <li>AI Integration: Utilizes an AI API key provided by the library user, enabling dynamic and context-aware interactions.</li>
                            <li>Customizable and Flexible: Designed to be easily integrated into any website, providing developers with the flexibility to customize the chatbot's behavior and appearance.</li>
                            <li>Cross-Platform Compatibility: Built with compatibility across modern JavaScript frameworks, making it adaptable for diverse project requirements.</li>
                            <li>Developer-Friendly: Created with a focus on simplicity and ease of use, allowing developers to set up the chatbot with minimal configuration.</li>
                            <li>Published on NPM: The package is published on npm, making it easily accessible for public use. (search in npm : mchopan/sitebot)</li>
                        </ul>
                    </li>
                </ul>
            </div>

            <div class="project-item">
                <div class="project-header">
                    <h3>Campus Command <span>(React Native | Final Year Project)</span></h3>
                </div>
                <ul>
                    <li>Designed and developed a messaging app tailored for educational institutions.</li>
                    <li>Enabled colleges to create semesters/groups and manage student registrations by registration numbers.</li>
                    <li>Built a student dashboard that displays only relevant semester/group data.</li>
                    <li>Integrated features like group chat, updates, and collaboration tools.</li>
                    <li>Solely responsible for the app's design, development, and deployment.</li>
                </ul>
            </div>

            <div class="project-item">
                <div class="project-header">
                    <h3>Portfolio Website</h3>
                </div>
                <ul>
                    <li>Showcased professional skills and projects.</li>
                </ul>
            </div>

            <div class="project-item">
                <div class="project-header">
                    <h3>CASET College Website</h3>
                </div>
                <ul>
                    <li>Built a dynamic website for the college.</li>
                </ul>
            </div>

            <div class="project-item">
                <div class="project-header">
                    <h3>Mehndi Artist Portfolio</h3>
                </div>
                <ul>
                    <li>Designed an elegant portfolio for a mehndi artist.</li>
                </ul>
            </div>

            <div class="project-item">
                <div class="project-header">
                    <h3>News Website</h3>
                </div>
                <ul>
                    <li>Developed a news website leveraging the News API.</li>
                </ul>
            </div>

            <div class="project-item">
                <div class="project-header">
                    <h3>Facebook Clone</h3>
                </div>
                <ul>
                    <li>Built a front-end clone of Facebook.</li>
                </ul>
            </div>

            <div class="project-item">
                <div class="project-header">
                    <h3>Itinerary Website</h3>
                </div>
                <ul>
                    <li>Created a website for booking travel-related services.</li>
                </ul>
            </div>
        </section>

        <section class="education">
            <h2>Education</h2>
            <ul class="education-list">
                <li><strong>Caset College of Computer Science</strong><br>Bachelor of Computer Applications (BCA) | 2021 – 2023</li>
                <li><strong>Govt Boys Higher Secondary (JKBOSE)</strong> | 2019 – 2020</li>
                <li><strong>Maulana Azad Education Foundation</strong><br>Web Designing & Publishing Assistant | 2017 – 2018</li>
                <li><strong>Govt Boys High School (JKBOSE)</strong> | 2016 – 2017</li>
            </ul>
        </section>

        <section class="skills">
            <h2>Skills</h2>
            <ul class="skills-list">
                <li>Front-End: React, HTML, CSS, JavaScript, Material-UI</li>
                <li>Back-End: Node.js, Spring Boot, MySQL, MongoDB</li>
                <li>Mobile Development: React Native</li>
                <li>Other: Git, Web Designing & Publishing</li>
            </ul>
        </section>

        <section class="certifications">
            <h2>Certifications</h2>
            <ul class="certifications-list">
                <li>Full Stack Web Development – Red Stag Labs, Qamarwari, Srinagar</li>
                <li>Linux Fundamentals – Kimo.ai</li>
                <li>Hands-On Linux System Administration – Kimo.ai</li>
                <li>Data Structures and Algorithms – Kimo.ai</li>
            </ul>
        </section>
    </div>
</body>
</html>