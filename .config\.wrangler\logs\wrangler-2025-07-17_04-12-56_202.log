
--- 2025-07-17T04:12:56.412Z debug
🪵  Writing logs to "/home/<USER>/workspace/.config/.wrangler/logs/wrangler-2025-07-17_04-12-56_202.log"
---

--- 2025-07-17T04:12:56.412Z debug
Metrics dispatcher: Posting data {"deviceId":"94cc17b3-0f80-4847-8c99-9fee1bd4e703","event":"wrangler command started","timestamp":1752725576412,"properties":{"amplitude_session_id":1752725576410,"amplitude_event_id":0,"wranglerVersion":"4.24.4","osPlatform":"Linux","osVersion":"#1-NixOS SMP PREEMPT_DYNAMIC Tue Jan  1 00:00:00 UTC 1980","nodeVersion":20,"packageManager":"npm","isFirstUsage":false,"configFileType":"none","isCI":false,"isPagesCI":false,"isWorkersCI":false,"isInteractive":false,"hasAssets":false,"argsUsed":["j","projectName"],"argsCombination":"j, projectName","command":"wrangler pages deploy","args":{"projectName":"<REDACTED>","xJsonConfig":true,"j":true,"uploadSourceMaps":false,"directory":"<REDACTED>"}}}
---

--- 2025-07-17T04:12:56.425Z debug
.env file not found at ".env". Continuing... For more details, refer to https://developers.cloudflare.com/workers/wrangler/system-environment-variables/
---

--- 2025-07-17T04:12:56.490Z log

 ⛅️ wrangler 4.24.4
───────────────────
---

--- 2025-07-17T04:12:56.495Z debug
Saving to cache: {"account":{"id":"dca21f9606d1acfc7b48277103239f40","name":""}}
---

--- 2025-07-17T04:12:56.497Z debug
-- START CF API REQUEST: GET https://api.cloudflare.com/client/v4/accounts/dca21f9606d1acfc7b48277103239f40/pages/projects/ai-website-46a3cfa3
---

--- 2025-07-17T04:12:56.497Z debug
QUERY STRING: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T04:12:56.497Z debug
HEADERS: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T04:12:56.497Z debug
INIT: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T04:12:56.497Z debug
-- END CF API REQUEST
---

--- 2025-07-17T04:12:56.778Z debug
-- START CF API RESPONSE: Not Found 404
---

--- 2025-07-17T04:12:56.779Z debug
HEADERS: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T04:12:56.779Z debug
RESPONSE: omitted; set WRANGLER_LOG_SANITIZE=false to include sanitized data
---

--- 2025-07-17T04:12:56.779Z debug
-- END CF API RESPONSE
---

--- 2025-07-17T04:12:56.783Z log

---

--- 2025-07-17T04:12:56.834Z error
[31m✘ [41;31m[[41;97mERROR[41;31m][0m [1mThis command cannot be run in a non-interactive context[0m


---

--- 2025-07-17T04:12:56.835Z debug
Error: This command cannot be run in a non-interactive context
    at select (/home/<USER>/workspace/node_modules/wrangler/wrangler-dist/cli.js:52807:13)
    at promptSelectExistingOrNewProject (/home/<USER>/workspace/node_modules/wrangler/wrangler-dist/cli.js:92629:10)
    at Object.handler (/home/<USER>/workspace/node_modules/wrangler/wrangler-dist/cli.js:92831:35)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Object.handler (/home/<USER>/workspace/node_modules/wrangler/wrangler-dist/cli.js:82454:7)
---

--- 2025-07-17T04:12:56.836Z debug
Metrics dispatcher: Posting data {"deviceId":"94cc17b3-0f80-4847-8c99-9fee1bd4e703","event":"wrangler command errored","timestamp":1752725576836,"properties":{"amplitude_session_id":1752725576410,"amplitude_event_id":1,"wranglerVersion":"4.24.4","osPlatform":"Linux","osVersion":"#1-NixOS SMP PREEMPT_DYNAMIC Tue Jan  1 00:00:00 UTC 1980","nodeVersion":20,"packageManager":"npm","isFirstUsage":false,"configFileType":"none","isCI":false,"isPagesCI":false,"isWorkersCI":false,"isInteractive":false,"hasAssets":false,"argsUsed":["j","projectName"],"argsCombination":"j, projectName","command":"wrangler pages deploy","args":{"projectName":"<REDACTED>","xJsonConfig":true,"j":true,"uploadSourceMaps":false,"directory":"<REDACTED>"},"durationMs":447,"durationSeconds":0.447,"durationMinutes":0.00745,"errorType":"NoDefaultValueProvided","errorMessage":"This command cannot be run in a non-interactive context"}}
---
